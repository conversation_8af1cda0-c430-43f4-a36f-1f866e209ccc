Jan 25 00:00:00 ar_logcat: Run Ar_logcat service.
Jan 25 00:00:00 ar_logcat: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:00 ar_logcat: vin start @ sdk version [5hJk2Ld8Rt9sFpQw] 
Jan 25 00:00:00 ar_logcat: ^[[35;22m[124390381][0298][0x7f76dad200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3693 load vin driver start^[[0m
Jan 25 00:00:00 ar_logcat: ^[[31;22m[124390387][0298][0x7f76dad200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:00 ar_logcat: ^[[35;22m[124390388][0298][0x7f76dad200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3957 load vin driver end max_sensor_count=5^[[0m
Jan 25 00:00:00 ar_logcat: Create socked success
Jan 25 00:00:00 ar_logcat: Bind socket success
Jan 25 00:00:00 ar_logcat: Listen socket success
Jan 25 00:00:00 ar_logcat: client accept thread running
Jan 25 00:00:01 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.976083][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.976589][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.977792][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.977841][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390395][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390395][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390395][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390396][0298][0x7f760f7200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 358e7000 35b05000 35b8e000 0 0 0 35c11000 35e2f000 35eb8000 0 0 0 
Jan 25 00:00:01 ar_logcat: aec index -1589366767 line 1125 gain 1.000000 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390398][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390455][0298][0x7f760b5200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390455][0298][0x7f760b5200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390456][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390456][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390456][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390456][0298][0x7f7584f200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390456][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 ar_logcat: 35f41000 3615f000 361e8000 0 0 0 3626b000 36489000 36512000 0 0 0 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390456][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 ar_logcat: aec index -2059128784 line 1125 gain 1.000000 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390456][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390456][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390457][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 3659b000 367b9000 36842000 0 0 0 368c5000 36ae3000 36b6c000 0 0 0 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390458][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390458][0298][0x7f76118200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ao s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 ar_logcat: ^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390459][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390459][0298][0x7f771d0200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ai s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 ar_logcat: ^[[0m
Jan 25 00:00:02 ar_logcat: open the driver sns:imx681 obj:stSnsimx681Obj lib:libsns_imx681.so 
Jan 25 00:00:02 ar_logcat: ^[[31;22m[25 00:00:02.270558][0447][0x7f6cff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3554 !!!!!!!!!! Note:your app cfg the isp read and vif write  as no compress,this mode isp read and vif write will cost more 
Jan 25 00:00:02 ar_logcat: ^[[31;22m[25 00:00:02.270726][0447][0x7f6cff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3657 stream id 0, no need cfg the sns_var_info^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390524][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[33;22m[124390528][0298][0x7f760f7200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:02 ar_logcat: ^[[33;22m[124390528][0298][0x7f760f7200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390528][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390529][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390530][0298][0x7f76118200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:02 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:02 ar_logcat: ^[[31;22m[25 00:00:02.334874][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:02 ar_logcat: ^[[31;22m[25 00:00:02.334904][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:02 ar_logcat: ^[[31;22m[25 00:00:02.335014][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:02 ar_logcat: ^[[31;22m[25 00:00:02.335024][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390531][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390531][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390531][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390531][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390531][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390531][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390531][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:02 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390531][0298][0x7f7568a200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:02 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:02 ar_logcat: 35f41000 3615f000 361e8000 0 0 0 3626b000 36489000 36512000 0 0 0 
Jan 25 00:00:02 ar_logcat: aec index 1091836049 line 1125 gain 1.000000 
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390531][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390531][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390532][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390532][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:02 ar_logcat: 3659b000 367b9000 36842000 0 0 0 368c5000 36ae3000 36b6c000 0 0 0 
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390532][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390532][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4384 power on vc 0 0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390533][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390534][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390535][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4386 power on vc end 0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390542][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390542][0298][0x7f7568a200][SYS_CORE][VI_KEY] mipi_rx_9411.c: mipi_rx_power_up_9411 : 64 enable the mipi cfg and pcs clock, and set pcs to clk=100000000hz^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390542][0298][0x7f7568a200][SYS_CORE][VI_KEY] mipi_rx.c: one_mipi_init : 1204 mipi 4 init done^[[0m
Jan 25 00:00:02 ar_logcat: aec index 58856738 line 7042 gain 1.000000 
Jan 25 00:00:02 ar_logcat:  imx681_stream_on
Jan 25 00:00:02 ar_logcat:  imx681_trigger_on
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390542][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390542][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] stream.c: link_filter_external : 407 check_linking_stream error:-2146402228^[[0m
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390543][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390543][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=1^[[0m
Jan 25 00:00:02 ar_logcat: aec index 1073743872 line 7042 gain 1.000000 
Jan 25 00:00:02 ar_logcat:  imx681_stream_on
Jan 25 00:00:02 ar_logcat:  imx681_trigger_on
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390543][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390547][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390549][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390549][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390549][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 2^[[0m
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390551][0482][0x7f7dd2e010][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:02 ar_logcat: ^[[33;22m[124390551][0298][0x7f760b5200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:02 ar_logcat: ^[[33;22m[124390551][0298][0x7f760b5200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390551][0298][0x7f760b5200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:N] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:02 ar_logcat: ^[[33;22m[124390551][0298][0x7f771d0200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390551][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390551][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390552][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390552][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390552][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:02 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390552][0298][0x7f771d0200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:02 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390552][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:02 ar_logcat: 35f41000 3615f000 361e8000 0 0 0 3626b000 36489000 36512000 0 0 0 
Jan 25 00:00:02 ar_logcat: aec index 1094762499 line 1125 gain 1.000000 
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390552][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390552][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:02 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:02 ar_logcat: ^[[31;22m[124390552][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:02 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:02 ar_logcat: 3659b000 367b9000 36842000 0 0 0 368c5000 36ae3000 36b6c000 0 0 0 
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390553][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[33;22m[124390554][0298][0x7f75870200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_enable : 456 subvision already enable!^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390554][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390556][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390556][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:02 ar_logcat: ^[[33;22m[124390556][0298][0x7f75870200][VO_CORE][WARN] display_top.c: display_offset_vo_src : 1520 hardware_diff_us:6635.116699 first_skewing_us:6635.116699!
Jan 25 00:00:02 ar_logcat: ^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390559][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:02 ar_logcat: ^[[35;22m[124390559][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390616][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:03 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390619][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:05 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390798][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [dp_scaler_lut_direct_mode_1] failed!^[[0m
Jan 25 00:00:05 ar_logcat: yuv420 out, user have set the lut, use user lut
Jan 25 00:00:06 ar_logcat: ^[[35;22m[124390953][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:06 ar_logcat: ^[[35;22m[124390953][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:06 ar_logcat: ^[[35;22m[124390954][0298][0x7f7578b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[124391001][0298][0x7f75870200][VO_CORE][ERROR] display_top.c: display_offset_vo_src : 1510 src_de_over_time_us:-12544.269531 is more than mod_de_time_us:11110.129883(one frame), skewing_err_cnt:1^[[0m
Jan 25 00:00:07 ar_logcat: reference_clock:        300.000000Mhz
Jan 25 00:00:07 ar_logcat: src_vsync_sel:          SRC_DP_ISP
Jan 25 00:00:07 ar_logcat: de_vsync_sel:           DE1
Jan 25 00:00:07 ar_logcat: src_vsync_polarity:     0
Jan 25 00:00:07 ar_logcat: de_vsync_polarity:      0
Jan 25 00:00:07 ar_logcat: src_frame               16666.042969 us, 60.002245 fps
Jan 25 00:00:07 ar_logcat: de_frame                11110.129883 us, 90.007949 fps
Jan 25 00:00:07 ar_logcat: vsync_delay_time(limit) 9675.993164 us
Jan 25 00:00:07 ar_logcat: vsync_delay_time(over)  -12544.269531 us
Jan 25 00:00:07 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.160176][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.160251][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.160402][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.160415][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391013][0298][0x7f76dad200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391013][0298][0x7f7576a200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391013][0298][0x7f755a7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391013][0298][0x7f755a7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391014][0298][0x7f760b5200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391014][0298][0x7f760b5200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391014][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391014][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391014][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 ar_logcat: 35f41000 3637b000 3648b000 0 0 0 36595000 369cf000 36adf000 0 0 0 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391014][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 ar_logcat: aec index 22022179 line 1125 gain 1.000000 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391014][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391014][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391014][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391015][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391016][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.209671][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.209705][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.211899][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.211926][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391018][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391018][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0298][0x7f75586200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 ar_logcat: 35f41000 3637b000 3648b000 0 0 0 36595000 369cf000 36adf000 0 0 0 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 ar_logcat: aec index 22022179 line 1125 gain 1.000000 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0298][0x7f75586200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0298][0x7f75586200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 ar_logcat: 3659b000 369d5000 36ae5000 0 0 0 36bef000 37029000 37139000 0 0 0 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391021][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391023][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391035][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391035][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[124391038][0482][0x7f4e72f1a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391038][0298][0x7f760f7200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[124391038][0298][0x7f760f7200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391039][0298][0x7f7568a200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391039][0298][0x7f760f7200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391039][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391039][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391039][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391039][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 ar_logcat: 35f41000 3637b000 3648b000 0 0 0 36595000 369cf000 36adf000 0 0 0 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391039][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391039][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 ar_logcat: aec index 1094762499 line 1125 gain 1.000000 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391039][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391039][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 ar_logcat: 3659b000 369d5000 36ae5000 0 0 0 36bef000 37029000 37139000 0 0 0 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391041][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391043][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:11 ar_logcat: ^[[33;22m[124391432][0298][0x7f760b5200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:11 ar_logcat: ^[[33;22m[124391432][0298][0x7f7576a200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391432][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391433][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391434][0298][0x7f76dad200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:11 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:11 ar_logcat: ^[[31;22m[25 00:00:11.373451][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:11 ar_logcat: ^[[31;22m[25 00:00:11.373490][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:11 ar_logcat: ^[[31;22m[25 00:00:11.373610][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:11 ar_logcat: ^[[31;22m[25 00:00:11.373621][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391434][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391435][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391435][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391435][0298][0x7f755c8200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391435][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:11 ar_logcat: 35f41000 3637b000 3648b000 0 0 0 36595000 369cf000 36adf000 0 0 0 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391435][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391435][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:11 ar_logcat: aec index 22022179 line 1125 gain 1.000000 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391435][0298][0x7f755c8200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391435][0298][0x7f755c8200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:11 ar_logcat: 3659b000 369d5000 36ae5000 0 0 0 36bef000 37029000 37139000 0 0 0 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391438][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391440][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391451][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391453][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:11 ar_logcat: ^[[31;22m[124391454][0482][0x7ed60771a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:11 ar_logcat: ^[[33;22m[124391455][0298][0x7f755a7200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:11 ar_logcat: ^[[31;22m[124391455][0298][0x7f755a7200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:98][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391455][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391456][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391456][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391456][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:11 ar_logcat: 35f41000 3637b000 3648b000 0 0 0 36595000 369cf000 36adf000 0 0 0 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391456][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:11 ar_logcat: aec index 1094762499 line 1125 gain 1.000000 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391456][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391456][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:11 ar_logcat: 3659b000 369d5000 36ae5000 0 0 0 36bef000 37029000 37139000 0 0 0 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391458][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391460][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
