[    0.372817] sw reset
[    0.373724] sw reset
[    0.375009] aw35615 device ID: 0x91
[    0.376140] pd := off
[    0.376142] vbus is already Off
[    0.376143] charge is already Off
[    0.376145] vconn is already Off
[    0.376358] pd header := Sink, Device
[    0.376368] cc1=Open, cc2=Open
[    0.377605] pd := off
[    0.377608] vbus is already Off
[    0.377609] charge is already Off
[    0.377611] vconn is already Off
[    0.377842] pd header := Sink, Device
[    0.377850] cc := Rd
[    0.380347] start drp toggling
[    0.381055] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.381058] IRQ: VBUS_OK, vbus=On
[    0.381066] gpio_intn_value:0
[    0.381176] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.381718] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.381720] IRQ: VBUS_OK, vbus=On
[    0.381721] gpio_intn_value:1
[    0.381830] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.387987] IRQ: 0x00, a: 0x40, b: 0x00, status0: 0x82, status1: 0x28
[    0.387990] IRQ: TOGDONE
[    0.389425] detected cc1=Open, cc2=Rp-1.5
[    0.389428] gpio_intn_value:0
[    0.389537] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.390077] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0x82, status1: 0x28
[    0.390080] gpio_intn_value:1
[    0.390189] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.390193] cc1=Open, cc2=Rp-1.5
[    0.458825] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.458832] IRQ: BC_LVL, handler pending
[    0.458840] gpio_intn_value:0
[    0.458950] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.459494] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.459498] IRQ: BC_LVL, handler pending
[    0.459502] gpio_intn_value:1
[    0.459611] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.460339] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.460344] IRQ: BC_LVL, handler pending
[    0.460348] gpio_intn_value:0
[    0.460459] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.461014] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.461018] IRQ: BC_LVL, handler pending
[    0.461023] gpio_intn_value:1
[    0.461132] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.462078] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.462083] IRQ: BC_LVL, handler pending
[    0.462086] gpio_intn_value:0
[    0.462195] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.462734] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.462736] IRQ: BC_LVL, handler pending
[    0.462738] gpio_intn_value:1
[    0.462847] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.498553] BC_LVL handler, status0=0x92
[    0.564205] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.564217] IRQ: BC_LVL, handler pending
[    0.564224] gpio_intn_value:0
[    0.564333] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.564872] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.564875] IRQ: BC_LVL, handler pending
[    0.564876] gpio_intn_value:1
[    0.564985] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.565761] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.565765] IRQ: BC_LVL, handler pending
[    0.565767] gpio_intn_value:0
[    0.565877] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.566420] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.566422] IRQ: BC_LVL, handler pending
[    0.566424] gpio_intn_value:1
[    0.566534] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.567445] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.567452] IRQ: BC_LVL, handler pending
[    0.567460] gpio_intn_value:0
[    0.567568] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.568123] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.568128] IRQ: BC_LVL, handler pending
[    0.568136] gpio_intn_value:1
[    0.568250] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.590522] pd header := Sink, Device
[    0.590565] vbus is already Off
[    0.591776] pd := on
[    0.598551] BC_LVL handler, status0=0x92
[    0.669661] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.669671] IRQ: BC_LVL, handler pending
[    0.669678] gpio_intn_value:0
[    0.669787] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.670326] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.670328] IRQ: BC_LVL, handler pending
[    0.670330] gpio_intn_value:0
[    0.670438] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.670979] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.670981] IRQ: BC_LVL, handler pending
[    0.670983] IRQ: PD sent good CRC
[    0.671581] PD message header: 17a1 len:4 crc:2db8a2a
[    0.671589] gpio_intn_value:1
[    0.671699] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.672398] sending PD message header: 1042
[    0.672402] sending PD message len: 4
[    0.673245] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.673249] IRQ: BC_LVL, handler pending
[    0.673255] gpio_intn_value:0
[    0.673364] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.673904] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.673907] IRQ: BC_LVL, handler pending
[    0.673909] gpio_intn_value:0
[    0.674019] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.674561] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xd2, status1: 0x08
[    0.674563] IRQ: BC_LVL, handler pending
[    0.674564] IRQ: PD tx success
[    0.674991] PD message header: 1a1 len:0 crc:81c2afc1
[    0.675003] gpio_intn_value:0
[    0.675114] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.675656] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.675659] IRQ: BC_LVL, handler pending
[    0.675663] IRQ: PD sent good CRC
[    0.676083] PD message header: 963 len:0 crc:76d5923f
[    0.676089] gpio_intn_value:0
[    0.676204] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.676743] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.676745] IRQ: BC_LVL, handler pending
[    0.676748] gpio_intn_value:0
[    0.676857] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.677396] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.677398] IRQ: BC_LVL, handler pending
[    0.677399] IRQ: PD sent good CRC
[    0.677816] PD message header: b66 len:0 crc:e5ac0756
[    0.677820] gpio_intn_value:0
[    0.677933] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.678548] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0x92, status1: 0x28
[    0.678553] IRQ: BC_LVL, handler pending
[    0.678560] gpio_intn_value:1
[    0.678673] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.718551] BC_LVL handler, status0=0x92
[    0.728190] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.728196] IRQ: BC_LVL, handler pending
[    0.728203] gpio_intn_value:0
[    0.728312] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.728873] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.728876] IRQ: BC_LVL, handler pending
[    0.728877] gpio_intn_value:0
[    0.728986] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.729525] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.729527] IRQ: BC_LVL, handler pending
[    0.729528] IRQ: PD sent good CRC
[    0.730124] PD message header: 1d6f len:4 crc:a49bdc77
[    0.730130] gpio_intn_value:1
[    0.730239] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.731215] sending PD message header: 524f
[    0.731217] sending PD message len: 20
[    0.731803] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.731805] IRQ: BC_LVL, handler pending
[    0.731806] gpio_intn_value:0
[    0.731915] aw35615_i2c_read AW_REG_STATUS1 :0x20
[    0.732453] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x28
[    0.732455] IRQ: BC_LVL, handler pending
[    0.732457] gpio_intn_value:0
[    0.732565] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.733104] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.733108] IRQ: BC_LVL, handler pending
[    0.733109] gpio_intn_value:0
[    0.733218] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.733756] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xd1, status1: 0x08
[    0.733758] IRQ: BC_LVL, handler pending
[    0.733759] IRQ: PD tx success
[    0.734176] PD message header: 361 len:0 crc:a43619a3
[    0.734180] gpio_intn_value:0
[    0.734289] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.734827] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.734829] IRQ: BC_LVL, handler pending
[    0.734831] gpio_intn_value:0
[    0.734939] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.735478] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.735480] IRQ: BC_LVL, handler pending
[    0.735481] IRQ: PD sent good CRC
[    0.736075] PD message header: 1f6f len:4 crc:ccee20f9
[    0.736078] gpio_intn_value:1
[    0.736187] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.736879] sending PD message header: 244f
[    0.736880] sending PD message len: 8
[    0.737589] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.737591] IRQ: BC_LVL, handler pending
[    0.737593] gpio_intn_value:0
[    0.737701] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.738239] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.738241] IRQ: BC_LVL, handler pending
[    0.738243] gpio_intn_value:0
[    0.738351] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.738891] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.738893] IRQ: BC_LVL, handler pending
[    0.738894] IRQ: PD tx success
[    0.739311] PD message header: 561 len:0 crc:4d55bc96
[    0.739316] gpio_intn_value:0
[    0.739425] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.739970] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.739972] IRQ: BC_LVL, handler pending
[    0.739973] gpio_intn_value:0
[    0.740082] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.740620] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.740622] IRQ: BC_LVL, handler pending
[    0.740623] IRQ: PD sent good CRC
[    0.741217] PD message header: 116f len:4 crc:d279c8bc
[    0.741220] gpio_intn_value:0
[    0.741329] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.741868] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0x92, status1: 0x28
[    0.741870] IRQ: BC_LVL, handler pending
[    0.741871] gpio_intn_value:1
[    0.741979] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.742671] sending PD message header: 264f
[    0.742673] sending PD message len: 8
[    0.743327] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.743329] IRQ: BC_LVL, handler pending
[    0.743331] gpio_intn_value:0
[    0.743439] aw35615_i2c_read AW_REG_STATUS1 :0x20
[    0.743977] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.743979] IRQ: BC_LVL, handler pending
[    0.743981] gpio_intn_value:0
[    0.744089] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.744628] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.744630] IRQ: BC_LVL, handler pending
[    0.744631] IRQ: PD tx success
[    0.745048] PD message header: 761 len:0 crc:a35bddba
[    0.745051] gpio_intn_value:0
[    0.745160] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.745700] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.745702] IRQ: BC_LVL, handler pending
[    0.745703] gpio_intn_value:0
[    0.745812] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.746350] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.746352] IRQ: BC_LVL, handler pending
[    0.746353] IRQ: PD sent good CRC
[    0.746948] PD message header: 136f len:4 crc:34acc952
[    0.746951] gpio_intn_value:1
[    0.747060] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.747808] sending PD message header: 184f
[    0.747811] sending PD message len: 4
[    0.748545] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.748548] IRQ: BC_LVL, handler pending
[    0.748556] gpio_intn_value:0
[    0.748666] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.749205] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.749207] IRQ: BC_LVL, handler pending
[    0.749208] gpio_intn_value:0
[    0.749317] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.749855] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xd1, status1: 0x08
[    0.749857] IRQ: BC_LVL, handler pending
[    0.749859] IRQ: PD tx success
[    0.750277] PD message header: 961 len:0 crc:44e3f0bd
[    0.750282] gpio_intn_value:0
[    0.750392] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.750930] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.750932] IRQ: BC_LVL, handler pending
[    0.750934] gpio_intn_value:0
[    0.751042] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.751581] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.751584] IRQ: BC_LVL, handler pending
[    0.751585] IRQ: PD sent good CRC
[    0.752273] PD message header: 256f len:8 crc:e05e5346
[    0.752276] gpio_intn_value:1
[    0.752385] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.753079] sending PD message header: 2a4f
[    0.753081] sending PD message len: 8
[    0.753707] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.753709] IRQ: BC_LVL, handler pending
[    0.753710] gpio_intn_value:0
[    0.753818] aw35615_i2c_read AW_REG_STATUS1 :0x20
[    0.754357] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc0, status1: 0x08
[    0.754359] IRQ: BC_LVL, handler pending
[    0.754360] gpio_intn_value:0
[    0.754469] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.755007] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.755009] IRQ: BC_LVL, handler pending
[    0.755010] IRQ: PD tx success
[    0.755427] PD message header: b61 len:0 crc:aaed9191
[    0.755431] gpio_intn_value:0
[    0.755539] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.756077] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd0, status1: 0x08
[    0.756079] IRQ: BC_LVL, handler pending
[    0.756081] gpio_intn_value:0
[    0.756189] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.756728] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.756730] IRQ: BC_LVL, handler pending
[    0.756731] IRQ: PD sent good CRC
[    0.757419] PD message header: 276f len:8 crc:98dceb3b
[    0.757422] gpio_intn_value:0
[    0.757537] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.758135] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x28
[    0.758137] IRQ: BC_LVL, handler pending
[    0.758141] gpio_intn_value:1
[    0.758250] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.758857] sending PD message header: 1c4f
[    0.758859] sending PD message len: 4
[    0.759448] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.759450] IRQ: BC_LVL, handler pending
[    0.759454] gpio_intn_value:0
[    0.759563] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.760101] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.760103] IRQ: BC_LVL, handler pending
[    0.760104] gpio_intn_value:0
[    0.760213] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.760798] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.760802] IRQ: BC_LVL, handler pending
[    0.760804] IRQ: PD tx success
[    0.761261] PD message header: d61 len:0 crc:438e34a4
[    0.761274] gpio_intn_value:1
[    0.761410] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.779539] sending PD message header: 2e4f
[    0.779552] sending PD message len: 8
[    0.780322] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x28
[    0.780334] IRQ: BC_LVL, handler pending
[    0.780343] gpio_intn_value:0
[    0.780460] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.781077] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.781092] IRQ: BC_LVL, handler pending
[    0.781103] gpio_intn_value:0
[    0.781223] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.781799] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.781814] IRQ: BC_LVL, handler pending
[    0.781820] IRQ: PD tx success
[    0.782246] PD message header: f61 len:0 crc:ad805588
[    0.782266] gpio_intn_value:1
[    0.782380] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.818567] BC_LVL handler, status0=0x92
