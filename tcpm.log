[    0.375013] Setting usb_comm capable false
[    0.376146] Setting voltage/current limit 0 mV 0 mA
[    0.376149] polarity 0
[    0.376151] Requesting mux state 0, usb-role 0, orientation 0
[    0.376363] state change INVALID_STATE -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.376370] CC1: 0 -> 0, CC2: 0 -> 0 [state SNK_UNATTACHED, polarity 0, disconnected]
[    0.376374] 2-0022: registered
[    0.376375] Setting usb_comm capable false
[    0.377612] Setting voltage/current limit 0 mV 0 mA
[    0.377620] polarity 0
[    0.377622] Requesting mux state 0, usb-role 0, orientation 0
[    0.377848] cc:=2
[    0.379197] pending state change PORT_RESET -> PORT_RESET_WAIT_OFF @ 100 ms [rev1 NONE_AMS]
[    0.379203] state change PORT_RESET -> PORT_RESET_WAIT_OFF [delayed 100 ms]
[    0.379206] state change PORT_RESET_WAIT_OFF -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.379209] Start toggling
[    0.381835] VBUS on
[    0.390195] CC1: 0 -> 0, CC2: 0 -> 4 [state TOGGLING, polarity 0, connected]
[    0.390200] state change TOGGLING -> SNK_ATTACH_WAIT [rev1 NONE_AMS]
[    0.390204] pending state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED @ 200 ms [rev1 NONE_AMS]
[    0.590222] state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED [delayed 200 ms]
[    0.590230] state change SNK_DEBOUNCED -> SNK_ATTACHED [rev1 NONE_AMS]
[    0.590233] polarity 1
[    0.590236] Requesting mux state 1, usb-role 2, orientation 2
[    0.590528] state change SNK_ATTACHED -> SNK_STARTUP [rev1 NONE_AMS]
[    0.590552] state change SNK_STARTUP -> SNK_DISCOVERY [rev2 NONE_AMS]
[    0.590555] Setting voltage/current limit 5000 mV 1500 mA
[    0.590563] vbus=0 charge:=1
[    0.590567] state change SNK_DISCOVERY -> SNK_WAIT_CAPABILITIES [rev2 NONE_AMS]
[    0.591786] pending state change SNK_WAIT_CAPABILITIES -> HARD_RESET_SEND @ 620 ms [rev2 NONE_AMS]
[    0.671595] PD RX, header: 0x17a1 [1], 1 objects
[    0.671599] PD RX, object:0x36019096
[    0.671604]  PDO 0: type 0, 5000 mV, 1500 mA [RSHUD]
[    0.671738] state change SNK_WAIT_CAPABILITIES -> SNK_NEGOTIATE_CAPABILITIES [rev2 POWER_NEGOTIATION]
[    0.671746] Setting usb_comm capable true
[    0.671758] cc=2 cc1=0 cc2=4 vbus=0 vconn=sink polarity=1
[    0.671761] Requesting PDO 0: 5000 mV, 1500 mA [mismatch]
[    0.671763] PD TX, header: 0x1042
[    0.674997] PD TX complete, status: 0
[    0.675011] pending state change SNK_NEGOTIATE_CAPABILITIES -> HARD_RESET_SEND @ 60 ms [rev2 POWER_NEGOTIATION]
[    0.676102] PD RX, header: 0x963 [1], 0 objects
[    0.676108] state change SNK_NEGOTIATE_CAPABILITIES -> SNK_TRANSITION_SINK [rev2 POWER_NEGOTIATION]
[    0.676116] pending state change SNK_TRANSITION_SINK -> HARD_RESET_SEND @ 500 ms [rev2 POWER_NEGOTIATION]
[    0.677824] PD RX, header: 0xb66 [1], 0 objects
[    0.677831] Setting voltage/current limit 5000 mV 1500 mA
[    0.677847] state change SNK_TRANSITION_SINK -> SNK_READY [rev2 POWER_NEGOTIATION]
[    0.677982] AMS POWER_NEGOTIATION finished
[    0.730137] PD RX, header: 0x1d6f [1], 1 objects
[    0.730141] PD RX, object:0xff008001
[    0.730145] Rx VDM cmd 0xff008001 type 0 cmd 1 len 1 adev           (null)
[    0.730155] tcpm_queue_vdm
[    0.730157] vdm_run_state_machine vdm_state:1
[    0.730160] AMS DISCOVER_IDENTITY start
[    0.730163] vdm_run_state_machine vdm_state:4
[    0.730164] PD TX, header: 0x524f
[    0.734178] PD TX complete, status: 0
[    0.734182] AMS DISCOVER_IDENTITY finished
[    0.734187] vdm_run_state_machine vdm_state:2
[    0.734188] vdm_run_state_machine vdm_state:-1
[    0.736079] PD RX, header: 0x1f6f [1], 1 objects
[    0.736081] PD RX, object:0xff008002
[    0.736084] Rx VDM cmd 0xff008002 type 0 cmd 2 len 1 adev           (null)
[    0.736087] svid 0xff01
[    0.736088] tcpm_queue_vdm
[    0.736090] vdm_run_state_machine vdm_state:1
[    0.736091] AMS DISCOVER_SVIDS start
[    0.736092] vdm_run_state_machine vdm_state:4
[    0.736093] PD TX, header: 0x244f
[    0.739314] PD TX complete, status: 0
[    0.739318] AMS DISCOVER_SVIDS finished
[    0.739322] vdm_run_state_machine vdm_state:2
[    0.739323] vdm_run_state_machine vdm_state:-1
[    0.741221] PD RX, header: 0x116f [1], 1 objects
[    0.741227] PD RX, object:0xff018003
[    0.741229] Rx VDM cmd 0xff018003 type 0 cmd 3 len 1 adev           (null)
[    0.741231] SRC SVID 1: 0xff01
[    0.741233] tcpm_queue_vdm
[    0.741234] vdm_run_state_machine vdm_state:1
[    0.741235] AMS DISCOVER_MODES start
[    0.741236] vdm_run_state_machine vdm_state:4
[    0.741237] PD TX, header: 0x264f
[    0.745049] PD TX complete, status: 0
[    0.745053] AMS DISCOVER_MODES finished
[    0.745057] vdm_run_state_machine vdm_state:2
[    0.745058] vdm_run_state_machine vdm_state:-1
[    0.746952] PD RX, header: 0x136f [1], 1 objects
[    0.746953] PD RX, object:0xff018104
[    0.746957] Rx VDM cmd 0xff018104 type 0 cmd 4 len 1 adev ffffffc030b8e008
[    0.746960]  Alternate mode 0: SVID 0xff01, VDO 1: 0x00000405
[    0.747198] tcpm_queue_vdm
[    0.747202] vdm_run_state_machine vdm_state:1
[    0.747204] AMS DFP_TO_UFP_ENTER_MODE start
[    0.747205] vdm_run_state_machine vdm_state:4
[    0.747206] PD TX, header: 0x184f
[    0.750279] PD TX complete, status: 0
[    0.750285] AMS DFP_TO_UFP_ENTER_MODE finished
[    0.750296] vdm_run_state_machine vdm_state:2
[    0.750297] vdm_run_state_machine vdm_state:-1
[    0.752278] PD RX, header: 0x256f [1], 2 objects
[    0.752282] PD RX, object:0xff018110
[    0.752283] PD RX, object:0x1
[    0.752286] Rx VDM cmd 0xff018110 type 0 cmd 16 len 2 adev ffffffc030b8e008
[    0.752318] tcpm_queue_vdm
[    0.752322] vdm_run_state_machine vdm_state:1
[    0.752324] AMS STRUCTURED_VDMS start
[    0.752326] vdm_run_state_machine vdm_state:4
[    0.752328] PD TX, header: 0x2a4f
[    0.755428] PD TX complete, status: 0
[    0.755432] AMS STRUCTURED_VDMS finished
[    0.755436] vdm_run_state_machine vdm_state:2
[    0.755437] vdm_run_state_machine vdm_state:-1
[    0.757423] PD RX, header: 0x276f [1], 2 objects
[    0.757427] PD RX, object:0xff018111
[    0.757428] PD RX, object:0x406
[    0.757429] Rx VDM cmd 0xff018111 type 0 cmd 17 len 2 adev ffffffc030b8e008
[    0.757454] tcpm_queue_vdm
[    0.757496] vdm_run_state_machine vdm_state:1
[    0.757497] AMS STRUCTURED_VDMS start
[    0.757498] vdm_run_state_machine vdm_state:4
[    0.757500] PD TX, header: 0x1c4f
[    0.761266] PD TX complete, status: 0
[    0.761281] AMS STRUCTURED_VDMS finished
[    0.761287] vdm_run_state_machine vdm_state:2
[    0.761288] vdm_run_state_machine vdm_state:-1
[    0.778582] tcpm_queue_vdm
[    0.778660] vdm_run_state_machine vdm_state:1
[    0.778664] AMS ATTENTION start
[    0.778667] vdm_run_state_machine vdm_state:4
[    0.778669] PD TX, header: 0x2e4f
[    0.782255] PD TX complete, status: 0
[    0.782266] AMS ATTENTION finished
[    0.812299] vdm_run_state_machine vdm_state:2
[    0.812309] vdm_run_state_machine vdm_state:-1
