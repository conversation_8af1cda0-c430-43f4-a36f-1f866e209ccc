#!/usr/bin/env python3
"""
分析pilot.log文件中的left_gdc_config和right_gdc_config数据
可视化每行第一个数（average in 10s）和括号中的第二个数（max in 10s）
"""

import re
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime

def parse_pilot_log(filename):
    """
    解析pilot.log文件，提取left_gdc_config和right_gdc_config数据
    """
    left_data = []
    right_data = []
    
    # 正则表达式匹配模式
    # 例如: [left_gdc_config] 0.184 0.184 (0.072,20.430) 668 times in 10sec
    pattern = r'\[(\w+_gdc_config)\] (\d+\.\d+) \d+\.\d+ \((\d+\.\d+),(\d+\.\d+)\) \d+ times in 10sec'
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            match = re.search(pattern, line)
            if match:
                config_type = match.group(1)  # left_gdc_config 或 right_gdc_config
                average_10s = float(match.group(2))  # 第一个数（average in 10s）
                min_val = float(match.group(3))      # 括号中第一个数（min）
                max_10s = float(match.group(4))      # 括号中第二个数（max in 10s）
                
                # 提取时间戳
                time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                timestamp = time_match.group(1) if time_match else None
                
                data_point = {
                    'line_num': line_num,
                    'timestamp': timestamp,
                    'average_10s': average_10s,
                    'min_val': min_val,
                    'max_10s': max_10s
                }
                
                if config_type == 'left_gdc_config':
                    left_data.append(data_point)
                elif config_type == 'right_gdc_config':
                    right_data.append(data_point)
    
    return left_data, right_data

def create_time_series_plot(left_data, right_data):
    """
    创建时间序列折线图
    """
    # 转换为DataFrame
    left_df = pd.DataFrame(left_data)
    right_df = pd.DataFrame(right_data)

    # 创建图表
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('Left & Right GDC Config Time Series', fontsize=16)

    # 1. Average in 10s 时间序列图
    axes[0].plot(range(len(left_df)), left_df['average_10s'], 'b-', label='Left GDC', alpha=0.8, linewidth=2)
    axes[0].plot(range(len(right_df)), right_df['average_10s'], 'r-', label='Right GDC', alpha=0.8, linewidth=2)
    axes[0].set_title('Average in 10s (Time Series)', fontsize=12, fontweight='bold')
    axes[0].set_xlabel('Sample Index')
    axes[0].set_ylabel('Average Value (ms)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # 2. Max in 10s 时间序列图
    axes[1].plot(range(len(left_df)), left_df['max_10s'], 'b-', label='Left GDC', alpha=0.8, linewidth=2)
    axes[1].plot(range(len(right_df)), right_df['max_10s'], 'r-', label='Right GDC', alpha=0.8, linewidth=2)
    axes[1].set_title('Max in 10s (Time Series)', fontsize=12, fontweight='bold')
    axes[1].set_xlabel('Sample Index')
    axes[1].set_ylabel('Max Value (ms)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()
    
def print_statistics(left_data, right_data):
    """
    打印统计信息
    """
    left_df = pd.DataFrame(left_data)
    right_df = pd.DataFrame(right_data)
    
    print("=" * 60)
    print("GDC CONFIG 数据统计分析")
    print("=" * 60)
    
    print(f"\n数据点数量:")
    print(f"Left GDC Config: {len(left_df)} 个数据点")
    print(f"Right GDC Config: {len(right_df)} 个数据点")
    
    print(f"\nLeft GDC Config - Average in 10s 统计:")
    print(f"  平均值: {left_df['average_10s'].mean():.3f}")
    print(f"  中位数: {left_df['average_10s'].median():.3f}")
    print(f"  标准差: {left_df['average_10s'].std():.3f}")
    print(f"  最小值: {left_df['average_10s'].min():.3f}")
    print(f"  最大值: {left_df['average_10s'].max():.3f}")
    
    print(f"\nLeft GDC Config - Max in 10s 统计:")
    print(f"  平均值: {left_df['max_10s'].mean():.3f}")
    print(f"  中位数: {left_df['max_10s'].median():.3f}")
    print(f"  标准差: {left_df['max_10s'].std():.3f}")
    print(f"  最小值: {left_df['max_10s'].min():.3f}")
    print(f"  最大值: {left_df['max_10s'].max():.3f}")
    
    print(f"\nRight GDC Config - Average in 10s 统计:")
    print(f"  平均值: {right_df['average_10s'].mean():.3f}")
    print(f"  中位数: {right_df['average_10s'].median():.3f}")
    print(f"  标准差: {right_df['average_10s'].std():.3f}")
    print(f"  最小值: {right_df['average_10s'].min():.3f}")
    print(f"  最大值: {right_df['average_10s'].max():.3f}")
    
    print(f"\nRight GDC Config - Max in 10s 统计:")
    print(f"  平均值: {right_df['max_10s'].mean():.3f}")
    print(f"  中位数: {right_df['max_10s'].median():.3f}")
    print(f"  标准差: {right_df['max_10s'].std():.3f}")
    print(f"  最小值: {right_df['max_10s'].min():.3f}")
    print(f"  最大值: {right_df['max_10s'].max():.3f}")

def main():
    """
    主函数
    """
    log_file = 'pilot.log'
    
    print("正在解析pilot.log文件...")
    left_data, right_data = parse_pilot_log(log_file)
    
    if not left_data and not right_data:
        print("未找到GDC配置数据！")
        return
    
    print(f"解析完成！找到 {len(left_data)} 个left_gdc_config数据点，{len(right_data)} 个right_gdc_config数据点")
    
    # 打印统计信息
    print_statistics(left_data, right_data)

    # 创建时间序列图
    print("\n正在生成时间序列图表...")
    create_time_series_plot(left_data, right_data)

if __name__ == "__main__":
    main()
