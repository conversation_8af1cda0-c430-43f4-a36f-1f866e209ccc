#!/usr/bin/env python3
"""
分析pilot.log文件中的left_gdc_config和right_gdc_config数据
可视化每行第一个数（average in 10s）和括号中的第二个数（max in 10s）
"""

import re
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime
import numpy as np

def parse_pilot_log(filename):
    """
    解析pilot.log文件，提取left_gdc_config和right_gdc_config数据
    """
    left_data = []
    right_data = []
    
    # 正则表达式匹配模式
    # 例如: [left_gdc_config] 0.184 0.184 (0.072,20.430) 668 times in 10sec
    pattern = r'\[(\w+_gdc_config)\] (\d+\.\d+) \d+\.\d+ \((\d+\.\d+),(\d+\.\d+)\) \d+ times in 10sec'
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            match = re.search(pattern, line)
            if match:
                config_type = match.group(1)  # left_gdc_config 或 right_gdc_config
                average_10s = float(match.group(2))  # 第一个数（average in 10s）
                min_val = float(match.group(3))      # 括号中第一个数（min）
                max_10s = float(match.group(4))      # 括号中第二个数（max in 10s）
                
                # 提取时间戳
                time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                timestamp = time_match.group(1) if time_match else None
                
                data_point = {
                    'line_num': line_num,
                    'timestamp': timestamp,
                    'average_10s': average_10s,
                    'min_val': min_val,
                    'max_10s': max_10s
                }
                
                if config_type == 'left_gdc_config':
                    left_data.append(data_point)
                elif config_type == 'right_gdc_config':
                    right_data.append(data_point)
    
    return left_data, right_data

def create_visualizations(left_data, right_data):
    """
    创建可视化图表
    """
    # 转换为DataFrame
    left_df = pd.DataFrame(left_data)
    right_df = pd.DataFrame(right_data)

    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Left & Right GDC Config Analysis', fontsize=16)

    # 1. Average in 10s 时间序列图
    axes[0, 0].plot(range(len(left_df)), left_df['average_10s'], 'b-', label='Left GDC', alpha=0.7, linewidth=1.5)
    axes[0, 0].plot(range(len(right_df)), right_df['average_10s'], 'r-', label='Right GDC', alpha=0.7, linewidth=1.5)
    axes[0, 0].set_title('Average in 10s (Time Series)', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('Sample Index')
    axes[0, 0].set_ylabel('Average Value (ms)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. Max in 10s 时间序列图
    axes[0, 1].plot(range(len(left_df)), left_df['max_10s'], 'b-', label='Left GDC', alpha=0.7, linewidth=1.5)
    axes[0, 1].plot(range(len(right_df)), right_df['max_10s'], 'r-', label='Right GDC', alpha=0.7, linewidth=1.5)
    axes[0, 1].set_title('Max in 10s (Time Series)', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('Sample Index')
    axes[0, 1].set_ylabel('Max Value (ms)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. Average in 10s 分布直方图
    axes[1, 0].hist(left_df['average_10s'], bins=20, alpha=0.7, label='Left GDC', color='blue', edgecolor='black')
    axes[1, 0].hist(right_df['average_10s'], bins=20, alpha=0.7, label='Right GDC', color='red', edgecolor='black')
    axes[1, 0].set_title('Average in 10s Distribution', fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('Average Value (ms)')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. Max in 10s 分布直方图
    axes[1, 1].hist(left_df['max_10s'], bins=20, alpha=0.7, label='Left GDC', color='blue', edgecolor='black')
    axes[1, 1].hist(right_df['max_10s'], bins=20, alpha=0.7, label='Right GDC', color='red', edgecolor='black')
    axes[1, 1].set_title('Max in 10s Distribution', fontsize=12, fontweight='bold')
    axes[1, 1].set_xlabel('Max Value (ms)')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('gdc_config_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 创建箱线图比较
    fig2, axes2 = plt.subplots(1, 2, figsize=(12, 5))
    fig2.suptitle('GDC Config Box Plot Comparison', fontsize=16)
    
    # Average in 10s 箱线图
    data_avg = [left_df['average_10s'], right_df['average_10s']]
    axes2[0].boxplot(data_avg, tick_labels=['Left GDC', 'Right GDC'])
    axes2[0].set_title('Average in 10s', fontweight='bold')
    axes2[0].set_ylabel('Value (ms)')
    axes2[0].grid(True, alpha=0.3)

    # Max in 10s 箱线图
    data_max = [left_df['max_10s'], right_df['max_10s']]
    axes2[1].boxplot(data_max, tick_labels=['Left GDC', 'Right GDC'])
    axes2[1].set_title('Max in 10s', fontweight='bold')
    axes2[1].set_ylabel('Value (ms)')
    axes2[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('gdc_config_boxplot.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_detailed_analysis(left_data, right_data):
    """
    创建详细的分析图表
    """
    left_df = pd.DataFrame(left_data)
    right_df = pd.DataFrame(right_data)

    # 创建散点图和相关性分析
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Detailed GDC Config Analysis', fontsize=16)

    # 1. Average vs Max 散点图 - Left GDC
    axes[0, 0].scatter(left_df['average_10s'], left_df['max_10s'], alpha=0.6, color='blue', s=50)
    axes[0, 0].set_title('Left GDC: Average vs Max', fontweight='bold')
    axes[0, 0].set_xlabel('Average in 10s (ms)')
    axes[0, 0].set_ylabel('Max in 10s (ms)')
    axes[0, 0].grid(True, alpha=0.3)

    # 添加趋势线
    z = np.polyfit(left_df['average_10s'], left_df['max_10s'], 1)
    p = np.poly1d(z)
    axes[0, 0].plot(left_df['average_10s'], p(left_df['average_10s']), "r--", alpha=0.8)

    # 2. Average vs Max 散点图 - Right GDC
    axes[0, 1].scatter(right_df['average_10s'], right_df['max_10s'], alpha=0.6, color='red', s=50)
    axes[0, 1].set_title('Right GDC: Average vs Max', fontweight='bold')
    axes[0, 1].set_xlabel('Average in 10s (ms)')
    axes[0, 1].set_ylabel('Max in 10s (ms)')
    axes[0, 1].grid(True, alpha=0.3)

    # 添加趋势线
    z = np.polyfit(right_df['average_10s'], right_df['max_10s'], 1)
    p = np.poly1d(z)
    axes[0, 1].plot(right_df['average_10s'], p(right_df['average_10s']), "b--", alpha=0.8)

    # 3. 差值分析 (Left - Right)
    avg_diff = left_df['average_10s'] - right_df['average_10s']
    max_diff = left_df['max_10s'] - right_df['max_10s']

    axes[1, 0].plot(range(len(avg_diff)), avg_diff, 'g-', linewidth=2, alpha=0.7)
    axes[1, 0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[1, 0].set_title('Difference: Left - Right (Average)', fontweight='bold')
    axes[1, 0].set_xlabel('Sample Index')
    axes[1, 0].set_ylabel('Difference (ms)')
    axes[1, 0].grid(True, alpha=0.3)

    axes[1, 1].plot(range(len(max_diff)), max_diff, 'purple', linewidth=2, alpha=0.7)
    axes[1, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[1, 1].set_title('Difference: Left - Right (Max)', fontweight='bold')
    axes[1, 1].set_xlabel('Sample Index')
    axes[1, 1].set_ylabel('Difference (ms)')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('gdc_config_detailed_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def print_statistics(left_data, right_data):
    """
    打印统计信息
    """
    left_df = pd.DataFrame(left_data)
    right_df = pd.DataFrame(right_data)
    
    print("=" * 60)
    print("GDC CONFIG 数据统计分析")
    print("=" * 60)
    
    print(f"\n数据点数量:")
    print(f"Left GDC Config: {len(left_df)} 个数据点")
    print(f"Right GDC Config: {len(right_df)} 个数据点")
    
    print(f"\nLeft GDC Config - Average in 10s 统计:")
    print(f"  平均值: {left_df['average_10s'].mean():.3f}")
    print(f"  中位数: {left_df['average_10s'].median():.3f}")
    print(f"  标准差: {left_df['average_10s'].std():.3f}")
    print(f"  最小值: {left_df['average_10s'].min():.3f}")
    print(f"  最大值: {left_df['average_10s'].max():.3f}")
    
    print(f"\nLeft GDC Config - Max in 10s 统计:")
    print(f"  平均值: {left_df['max_10s'].mean():.3f}")
    print(f"  中位数: {left_df['max_10s'].median():.3f}")
    print(f"  标准差: {left_df['max_10s'].std():.3f}")
    print(f"  最小值: {left_df['max_10s'].min():.3f}")
    print(f"  最大值: {left_df['max_10s'].max():.3f}")
    
    print(f"\nRight GDC Config - Average in 10s 统计:")
    print(f"  平均值: {right_df['average_10s'].mean():.3f}")
    print(f"  中位数: {right_df['average_10s'].median():.3f}")
    print(f"  标准差: {right_df['average_10s'].std():.3f}")
    print(f"  最小值: {right_df['average_10s'].min():.3f}")
    print(f"  最大值: {right_df['average_10s'].max():.3f}")
    
    print(f"\nRight GDC Config - Max in 10s 统计:")
    print(f"  平均值: {right_df['max_10s'].mean():.3f}")
    print(f"  中位数: {right_df['max_10s'].median():.3f}")
    print(f"  标准差: {right_df['max_10s'].std():.3f}")
    print(f"  最小值: {right_df['max_10s'].min():.3f}")
    print(f"  最大值: {right_df['max_10s'].max():.3f}")

def export_to_csv(left_data, right_data):
    """
    导出数据到CSV文件
    """
    left_df = pd.DataFrame(left_data)
    right_df = pd.DataFrame(right_data)

    # 添加标识列
    left_df['config_type'] = 'left_gdc_config'
    right_df['config_type'] = 'right_gdc_config'

    # 合并数据
    combined_df = pd.concat([left_df, right_df], ignore_index=True)

    # 导出到CSV
    combined_df.to_csv('gdc_config_data.csv', index=False)

    # 也分别导出
    left_df.to_csv('left_gdc_config_data.csv', index=False)
    right_df.to_csv('right_gdc_config_data.csv', index=False)

    print("\nCSV文件已导出:")
    print("- gdc_config_data.csv (合并数据)")
    print("- left_gdc_config_data.csv (左侧数据)")
    print("- right_gdc_config_data.csv (右侧数据)")

def main():
    """
    主函数
    """
    log_file = 'pilot.log'
    
    print("正在解析pilot.log文件...")
    left_data, right_data = parse_pilot_log(log_file)
    
    if not left_data and not right_data:
        print("未找到GDC配置数据！")
        return
    
    print(f"解析完成！找到 {len(left_data)} 个left_gdc_config数据点，{len(right_data)} 个right_gdc_config数据点")
    
    # 打印统计信息
    print_statistics(left_data, right_data)
    
    # 创建可视化
    print("\n正在生成可视化图表...")
    create_visualizations(left_data, right_data)
    create_detailed_analysis(left_data, right_data)

    # 导出CSV数据
    export_to_csv(left_data, right_data)

    print("\n分析完成！图表已保存为:")
    print("- gdc_config_analysis.png")
    print("- gdc_config_boxplot.png")
    print("- gdc_config_detailed_analysis.png")

if __name__ == "__main__":
    main()
