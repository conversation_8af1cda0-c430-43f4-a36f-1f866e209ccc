#!/usr/bin/env python3
"""
分析pilot.log文件中的left_gdc_config和right_gdc_config数据
可视化每行第一个数（average in 10s）和括号中的第二个数（max in 10s）
"""

import re
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime

def parse_pilot_log(filename):
    """
    解析pilot.log文件，提取left_gdc_config、right_gdc_config、left_cost和msg_transfer数据
    """
    left_gdc_data = []
    right_gdc_data = []
    left_cost_data = []
    msg_transfer_data = []

    # 正则表达式匹配模式
    # GDC配置: [left_gdc_config] 0.184 0.184 (0.072,20.430) 668 times in 10sec
    gdc_pattern = r'\[(\w+_gdc_config)\] (\d+\.\d+) \d+\.\d+ \((\d+\.\d+),(\d+\.\d+)\) \d+ times in 10sec'

    # left_cost: [left_cost] 0.053 0.053 (0.001,0.482) 6014 times in 10sec
    left_cost_pattern = r'\[left_cost\] (\d+\.\d+) \d+\.\d+ \((\d+\.\d+),(\d+\.\d+)\) \d+ times in 10sec'

    # msg_transfer: [msg_transfer] 1.371 1.371 (1.371,1.371) 1 times in 10sec
    msg_transfer_pattern = r'\[msg_transfer\] (\d+\.\d+) \d+\.\d+ \((-?\d+\.\d+),(\d+\.\d+)\) \d+ times in 10sec'

    with open(filename, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            # 提取时间戳
            time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            timestamp = time_match.group(1) if time_match else None

            # 检查GDC配置数据
            gdc_match = re.search(gdc_pattern, line)
            if gdc_match:
                config_type = gdc_match.group(1)  # left_gdc_config 或 right_gdc_config
                average_10s = float(gdc_match.group(2))  # 第一个数（average in 10s）
                min_val = float(gdc_match.group(3))      # 括号中第一个数（min）
                max_10s = float(gdc_match.group(4))      # 括号中第二个数（max in 10s）

                data_point = {
                    'line_num': line_num,
                    'timestamp': timestamp,
                    'average_10s': average_10s,
                    'min_val': min_val,
                    'max_10s': max_10s
                }

                if config_type == 'left_gdc_config':
                    left_gdc_data.append(data_point)
                elif config_type == 'right_gdc_config':
                    right_gdc_data.append(data_point)

            # 检查left_cost数据
            left_cost_match = re.search(left_cost_pattern, line)
            if left_cost_match:
                average_10s = float(left_cost_match.group(1))  # 第一个数（average in 10s）
                min_val = float(left_cost_match.group(2))      # 括号中第一个数（min）
                max_10s = float(left_cost_match.group(3))      # 括号中第二个数（max in 10s）

                data_point = {
                    'line_num': line_num,
                    'timestamp': timestamp,
                    'average_10s': average_10s,
                    'min_val': min_val,
                    'max_10s': max_10s
                }
                left_cost_data.append(data_point)

            # 检查msg_transfer数据
            msg_transfer_match = re.search(msg_transfer_pattern, line)
            if msg_transfer_match:
                average_10s = float(msg_transfer_match.group(1))  # 第一个数（average in 10s）
                min_val = float(msg_transfer_match.group(2))      # 括号中第一个数（min，可能为负）
                max_10s = float(msg_transfer_match.group(3))      # 括号中第二个数（max in 10s）

                data_point = {
                    'line_num': line_num,
                    'timestamp': timestamp,
                    'average_10s': average_10s,
                    'min_val': min_val,
                    'max_10s': max_10s
                }
                msg_transfer_data.append(data_point)

    return left_gdc_data, right_gdc_data, left_cost_data, msg_transfer_data

def create_time_series_plot(left_gdc_data, right_gdc_data, left_cost_data, msg_transfer_data):
    """
    创建时间序列折线图
    """
    # 转换为DataFrame
    left_gdc_df = pd.DataFrame(left_gdc_data)
    right_gdc_df = pd.DataFrame(right_gdc_data)
    left_cost_df = pd.DataFrame(left_cost_data)
    msg_transfer_df = pd.DataFrame(msg_transfer_data)

    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Pilot Log Data Analysis - Time Series', fontsize=16, fontweight='bold')

    # 1. Left GDC Config - Average and Max in 10s 时间序列图
    if not left_gdc_df.empty:
        axes[0, 0].plot(range(len(left_gdc_df)), left_gdc_df['average_10s'], 'blue', label='Left GDC (Avg)', alpha=0.8, linewidth=2)
        axes[0, 0].plot(range(len(left_gdc_df)), left_gdc_df['max_10s'], 'red', label='Left GDC (Max)', alpha=0.8, linewidth=2)
    axes[0, 0].set_title('Left GDC Config Time Series', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('Sample Index')
    axes[0, 0].set_ylabel('Value (ms)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. Right GDC Config - Average and Max in 10s 时间序列图
    if not right_gdc_df.empty:
        axes[0, 1].plot(range(len(right_gdc_df)), right_gdc_df['average_10s'], 'blue', label='Right GDC (Avg)', alpha=0.8, linewidth=2)
        axes[0, 1].plot(range(len(right_gdc_df)), right_gdc_df['max_10s'], 'red', label='Right GDC (Max)', alpha=0.8, linewidth=2)
    axes[0, 1].set_title('Right GDC Config Time Series', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('Sample Index')
    axes[0, 1].set_ylabel('Value (ms)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. Left Cost - Average in 10s 时间序列图
    if not left_cost_df.empty:
        axes[1, 0].plot(range(len(left_cost_df)), left_cost_df['average_10s'], 'blue', label='Left Cost (Avg)', alpha=0.8, linewidth=2)
        axes[1, 0].plot(range(len(left_cost_df)), left_cost_df['max_10s'], 'red', label='Left Cost (Max)', alpha=0.8, linewidth=2)
    axes[1, 0].set_title('Left Cost Time Series', fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('Sample Index')
    axes[1, 0].set_ylabel('Value (ms)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. Msg Transfer - Average in 10s 时间序列图
    if not msg_transfer_df.empty:
        axes[1, 1].plot(range(len(msg_transfer_df)), msg_transfer_df['average_10s'], 'blue', label='Msg Transfer (Avg)', alpha=0.8, linewidth=2)
        axes[1, 1].plot(range(len(msg_transfer_df)), msg_transfer_df['max_10s'], 'red', label='Msg Transfer (Max)', alpha=0.8, linewidth=2)
    axes[1, 1].set_title('Msg Transfer Time Series', fontsize=12, fontweight='bold')
    axes[1, 1].set_xlabel('Sample Index')
    axes[1, 1].set_ylabel('Value (ms)')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()
    
def print_statistics(left_gdc_data, right_gdc_data, left_cost_data, msg_transfer_data):
    """
    打印统计信息
    """
    left_gdc_df = pd.DataFrame(left_gdc_data)
    right_gdc_df = pd.DataFrame(right_gdc_data)
    left_cost_df = pd.DataFrame(left_cost_data)
    msg_transfer_df = pd.DataFrame(msg_transfer_data)

    print("=" * 70)
    print("PILOT LOG 数据统计分析")
    print("=" * 70)

    print(f"\n数据点数量:")
    print(f"Left GDC Config: {len(left_gdc_df)} 个数据点")
    print(f"Right GDC Config: {len(right_gdc_df)} 个数据点")
    print(f"Left Cost: {len(left_cost_df)} 个数据点")
    print(f"Msg Transfer: {len(msg_transfer_df)} 个数据点")

    # Left GDC Config 统计
    if not left_gdc_df.empty:
        print(f"\nLeft GDC Config - Average in 10s 统计:")
        print(f"  平均值: {left_gdc_df['average_10s'].mean():.3f}")
        print(f"  中位数: {left_gdc_df['average_10s'].median():.3f}")
        print(f"  标准差: {left_gdc_df['average_10s'].std():.3f}")
        print(f"  最小值: {left_gdc_df['average_10s'].min():.3f}")
        print(f"  最大值: {left_gdc_df['average_10s'].max():.3f}")

        print(f"\nLeft GDC Config - Max in 10s 统计:")
        print(f"  平均值: {left_gdc_df['max_10s'].mean():.3f}")
        print(f"  中位数: {left_gdc_df['max_10s'].median():.3f}")
        print(f"  标准差: {left_gdc_df['max_10s'].std():.3f}")
        print(f"  最小值: {left_gdc_df['max_10s'].min():.3f}")
        print(f"  最大值: {left_gdc_df['max_10s'].max():.3f}")

    # Right GDC Config 统计
    if not right_gdc_df.empty:
        print(f"\nRight GDC Config - Average in 10s 统计:")
        print(f"  平均值: {right_gdc_df['average_10s'].mean():.3f}")
        print(f"  中位数: {right_gdc_df['average_10s'].median():.3f}")
        print(f"  标准差: {right_gdc_df['average_10s'].std():.3f}")
        print(f"  最小值: {right_gdc_df['average_10s'].min():.3f}")
        print(f"  最大值: {right_gdc_df['average_10s'].max():.3f}")

        print(f"\nRight GDC Config - Max in 10s 统计:")
        print(f"  平均值: {right_gdc_df['max_10s'].mean():.3f}")
        print(f"  中位数: {right_gdc_df['max_10s'].median():.3f}")
        print(f"  标准差: {right_gdc_df['max_10s'].std():.3f}")
        print(f"  最小值: {right_gdc_df['max_10s'].min():.3f}")
        print(f"  最大值: {right_gdc_df['max_10s'].max():.3f}")

    # Left Cost 统计
    if not left_cost_df.empty:
        print(f"\nLeft Cost - Average in 10s 统计:")
        print(f"  平均值: {left_cost_df['average_10s'].mean():.3f}")
        print(f"  中位数: {left_cost_df['average_10s'].median():.3f}")
        print(f"  标准差: {left_cost_df['average_10s'].std():.3f}")
        print(f"  最小值: {left_cost_df['average_10s'].min():.3f}")
        print(f"  最大值: {left_cost_df['average_10s'].max():.3f}")

        print(f"\nLeft Cost - Max in 10s 统计:")
        print(f"  平均值: {left_cost_df['max_10s'].mean():.3f}")
        print(f"  中位数: {left_cost_df['max_10s'].median():.3f}")
        print(f"  标准差: {left_cost_df['max_10s'].std():.3f}")
        print(f"  最小值: {left_cost_df['max_10s'].min():.3f}")
        print(f"  最大值: {left_cost_df['max_10s'].max():.3f}")

    # Msg Transfer 统计
    if not msg_transfer_df.empty:
        print(f"\nMsg Transfer - Average in 10s 统计:")
        print(f"  平均值: {msg_transfer_df['average_10s'].mean():.3f}")
        print(f"  中位数: {msg_transfer_df['average_10s'].median():.3f}")
        print(f"  标准差: {msg_transfer_df['average_10s'].std():.3f}")
        print(f"  最小值: {msg_transfer_df['average_10s'].min():.3f}")
        print(f"  最大值: {msg_transfer_df['average_10s'].max():.3f}")

        print(f"\nMsg Transfer - Max in 10s 统计:")
        print(f"  平均值: {msg_transfer_df['max_10s'].mean():.3f}")
        print(f"  中位数: {msg_transfer_df['max_10s'].median():.3f}")
        print(f"  标准差: {msg_transfer_df['max_10s'].std():.3f}")
        print(f"  最小值: {msg_transfer_df['max_10s'].min():.3f}")
        print(f"  最大值: {msg_transfer_df['max_10s'].max():.3f}")

def main():
    """
    主函数
    """
    log_file = 'pilot.log'

    print("正在解析pilot.log文件...")
    left_gdc_data, right_gdc_data, left_cost_data, msg_transfer_data = parse_pilot_log(log_file)

    if not left_gdc_data and not right_gdc_data and not left_cost_data and not msg_transfer_data:
        print("未找到任何相关数据！")
        return

    print(f"解析完成！")
    print(f"找到 {len(left_gdc_data)} 个left_gdc_config数据点")
    print(f"找到 {len(right_gdc_data)} 个right_gdc_config数据点")
    print(f"找到 {len(left_cost_data)} 个left_cost数据点")
    print(f"找到 {len(msg_transfer_data)} 个msg_transfer数据点")

    # 打印统计信息
    print_statistics(left_gdc_data, right_gdc_data, left_cost_data, msg_transfer_data)

    # 创建时间序列图
    print("\n正在生成时间序列图表...")
    create_time_series_plot(left_gdc_data, right_gdc_data, left_cost_data, msg_transfer_data)

if __name__ == "__main__":
    main()
