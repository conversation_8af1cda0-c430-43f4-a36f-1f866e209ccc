# Pilot.log GDC Config 分析工具

这个Python脚本用于分析pilot.log文件中的left_gdc_config和right_gdc_config数据，并生成可视化图表。

## 功能特性

- 解析pilot.log文件中的GDC配置数据
- 提取每行第一个数（average in 10s）和括号中的第二个数（max in 10s）
- 生成多种可视化图表
- 导出数据到CSV文件
- 提供详细的统计分析

## 生成的文件

### 图表文件
1. **gdc_config_analysis.png** - 主要分析图表
   - Average in 10s 时间序列图
   - Max in 10s 时间序列图
   - Average in 10s 分布直方图
   - Max in 10s 分布直方图

2. **gdc_config_boxplot.png** - 箱线图比较
   - Left vs Right GDC Average 比较
   - Left vs Right GDC Max 比较

3. **gdc_config_detailed_analysis.png** - 详细分析图表
   - Left GDC: Average vs Max 散点图（含趋势线）
   - Right GDC: Average vs Max 散点图（含趋势线）
   - Left - Right 差值分析（Average）
   - Left - Right 差值分析（Max）

### 数据文件
1. **gdc_config_data.csv** - 合并的所有数据
2. **left_gdc_config_data.csv** - 左侧GDC配置数据
3. **right_gdc_config_data.csv** - 右侧GDC配置数据

## 数据格式

CSV文件包含以下列：
- `line_num`: 日志文件中的行号
- `timestamp`: 时间戳
- `average_10s`: 10秒内的平均值
- `min_val`: 括号中的最小值
- `max_10s`: 括号中的最大值（10秒内）
- `config_type`: 配置类型（left_gdc_config 或 right_gdc_config）

## 使用方法

```bash
python3 analyze_pilot_log.py
```

确保pilot.log文件在同一目录下。

## 依赖库

- matplotlib
- pandas
- numpy

安装依赖：
```bash
pip install matplotlib pandas numpy
```

## 分析结果示例

根据当前数据分析：

### Left GDC Config
- Average in 10s: 平均值 0.256ms，范围 0.172-0.406ms
- Max in 10s: 平均值 3.977ms，范围 0.712-20.430ms

### Right GDC Config  
- Average in 10s: 平均值 0.137ms，范围 0.077-0.252ms
- Max in 10s: 平均值 3.775ms，范围 0.856-20.368ms

### 主要发现
- Left GDC的平均处理时间比Right GDC高约87%
- 两者的最大值范围相似，都有偶发的高延迟峰值
- 数据显示系统性能相对稳定，但存在偶发的性能波动

## 脚本特性

- 自动解析日志格式
- 生成高质量的可视化图表
- 提供详细的统计分析
- 支持数据导出，便于进一步分析
- 使用非交互式后端，适合服务器环境运行
