Jan 25 00:00:00 rcS: kernel start finish
Jan 25 00:00:00 rcS: Mpp service initpre
Jan 25 00:00:00 rcS: mount: mounting pstore on /sys/fs/pstore failed: No such file or directory
Jan 25 00:00:00 rcS: ls: /sys/fs/pstore: No such file or directory
Jan 25 00:00:00 rcS: insmod ar_osal.ko
Jan 25 00:00:00 rcS: insmod camera camera_pwr_ioctl.ko
Jan 25 00:00:00 rcS: insmod camera timestamp_record_exp.ko
Jan 25 00:00:00 rcS: insmod camera camera_plug_detect.ko
Jan 25 00:00:00 rcS: insmod ar_pack.ko
Jan 25 00:00:00 runsvdir: Run Mpp service.
Jan 25 00:00:00 runsvdir: Xreal core init.
Jan 25 00:00:00 runsvdir: /factory/start.conf not exist, please add it with your own config as following:
Jan 25 00:00:00 runsvdir: kern_log_level	1
Jan 25 00:00:00 runsvdir: nw_itf_name		eth0
Jan 25 00:00:00 runsvdir: nw_itf_mac		aa:bb:cc:dd:ee:ff
Jan 25 00:00:00 runsvdir: Run Mpp_vb_config service.
Jan 25 00:00:00 runsvdir: Current boot count is 1398
Jan 25 00:00:00 runsvdir: ./run: line 38: can't create /proc/irq/223/smp_affinity: nonexistent directory
Jan 25 00:00:00 runsvdir: run ...
Jan 25 00:00:00 runsvdir: Run Servicemanager service.
Jan 25 00:00:00 runsvdir: Run Command service.
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.407] [upgrade/upgrade.c] [init_upgrade_function-12] : media type : emmc
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.407] [upgrade/upgrade.c] [init_upgrade_function-13] : adjust emmc : true
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.407] [upgrade/upgrade.c] [init_upgrade_function-14] : adjust nand : false
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.407] [upgrade/upgrade.c] [init_upgrade_function-16] : emmc init
Jan 25 00:00:00 runsvdir: start parse factory json file!
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.410] [upgrade/emmc_upgrade.c] [get_mmc_start_part-489] : start from gpt : 1
Jan 25 00:00:00 runsvdir: SoC revision: 0
Jan 25 00:00:00 runsvdir: ================================
Jan 25 00:00:00 runsvdir: SoC ID: 21317
Jan 25 00:00:00 runsvdir: ================================
Jan 25 00:00:00 runsvdir: SoC chip ID: 2612742289705979130
Jan 25 00:00:00 runsvdir: ================================
Jan 25 00:00:00 runsvdir: SoC information:
Jan 25 00:00:00 runsvdir: Revision ID: 0
Jan 25 00:00:00 runsvdir: SoC suffix: 
Jan 25 00:00:00 runsvdir: SoC product ID: 21317
Jan 25 00:00:00 runsvdir: Soc PD year: 36
Jan 25 00:00:00 runsvdir: Soc PD week: 66
Jan 25 00:00:00 runsvdir: Soc ID info: 00956A6M
Jan 25 00:00:00 runsvdir: Soc SN: 59821306
Jan 25 00:00:00 runsvdir: ================================
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.411] [upgrade/emmc_upgrade.c] [get_mmc_param_start_part-522] : root number : 20
Jan 25 00:00:00 runsvdir: hw_version:GF_6
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.373] [main.c] [main-78] : pilot directory exist
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.373] [version.c] [read_pilot_version-67] : version file : /bin/pilot/pilot_version.txt
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.409] [version.c] [read_pilot_version-85] : pilot [/bin/pilot/pilot_version.txt] version [1.6.0.20250620150908]
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.409] [version.c] [read_pilot_version-67] : version file : /usrdata/pilot/pilot_version.txt
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.414] [version.c] [read_pilot_version-85] : pilot [/usrdata/pilot/pilot_version.txt] version [1.7.0.20250905152307]
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.414] [main.c] [main-101] : pilot in glass is equal to or greater than it in the file, skip copy
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.416] [command_service.c] [check_ipc_file-428] : file exist
Jan 25 00:00:00 runsvdir: Run Dropbear service.
Jan 25 00:00:00 runsvdir: net.core.wmem_default = 6000000
Jan 25 00:00:00 runsvdir: net.core.wmem_max = 6000000
Jan 25 00:00:00 runsvdir: net.ipv4.tcp_wmem = 40960 6000000 7254080
Jan 25 00:00:00 runsvdir: net.ipv4.tcp_rmem = 40960 6000000 7254080
Jan 25 00:00:00 runsvdir: vm.min_free_kbytes = 5000
Jan 25 00:00:00 runsvdir: kernel.core_pattern = /usrdata/log/current_log_dir/core.%e.%p
Jan 25 00:00:00 runsvdir: kernel.softlockup_panic = 1
Jan 25 00:00:00 runsvdir: kernel.softlockup_all_cpu_backtrace = 1
Jan 25 00:00:00 runsvdir: remove /usrdata/log/log_93
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: limit offline log number to 5.
Jan 25 00:00:00 runsvdir: Run Media service.
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: argc is error!!!
Jan 25 00:00:00 runsvdir: Run Audio service.
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: iManufacturer:XREAL
Jan 25 00:00:00 runsvdir: iProduct:XREAL One
Jan 25 00:00:00 runsvdir: VID:0x3318
Jan 25 00:00:00 runsvdir: PID (Kernel):0x0438
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: Run Ar watchdog service.
Jan 25 00:00:00 runsvdir: bootchartd is not running.
Jan 25 00:00:00 runsvdir: Run Pilot service.
Jan 25 00:00:00 runsvdir: Run power_manager service.
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: Property sys.init.boot_complete set to 1
Jan 25 00:00:00 runsvdir: Core init complete.
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: ar_clk exist, start to config sys clk
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: Initcore service exit, recycling resource.
Jan 25 00:00:00 runsvdir: disable clock 27
Jan 25 00:00:00 runsvdir: Property reached valid value: true
Jan 25 00:00:00 runsvdir: csn:H451X40483
Jan 25 00:00:00 runsvdir: Wait app_prepare done success.
Jan 25 00:00:00 runsvdir: usn:A1#H1X652H178615M
Jan 25 00:00:00 runsvdir: psn:XS4513010LMB1AM42H
Jan 25 00:00:00 runsvdir: open /dev/watchdog0 ret = 3 
Jan 25 00:00:00 runsvdir: disable clock 31
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: Property ro.bsp.usb_vid set to 0x3318
Jan 25 00:00:00 runsvdir: set clock 1, freq 300000000
Jan 25 00:00:00 runsvdir: set clock 10, freq 200000000
Jan 25 00:00:00 runsvdir: Property ro.bsp.usb_pid set to 0x0438
Jan 25 00:00:00 runsvdir: Property ro.bsp.product_name set to XREAL One
Jan 25 00:00:00 runsvdir: OK
Jan 25 00:00:00 runsvdir: Property ro.bsp.product_code set to GF
Jan 25 00:00:00 runsvdir: OK
Jan 25 00:00:00 runsvdir: Property ro.bsp.glasses_id set to H451X40483
Jan 25 00:00:00 runsvdir: set clock 13, freq 300000000
Jan 25 00:00:00 runsvdir: set clock 65, freq 300000000
Jan 25 00:00:00 runsvdir: Property ro.bsp.glasses_sn0 set to XS4513010LMB1AM42H
Jan 25 00:00:00 runsvdir: set clock 71, freq 300000000
Jan 25 00:00:00 runsvdir: Property ro.bsp.glasses_sn1 set to A1#H1X652H178615M
Jan 25 00:00:00 runsvdir: Property ro.bsp.hw_version set to GF_6
Jan 25 00:00:00 runsvdir: Set CPU frequency to 1296000 for mode middle
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: start ...
Jan 25 00:00:00 runsvdir: set S_VDD_0V8 and S_VDD_DOV8 to 787.5mV
Jan 25 00:00:00 runsvdir: USB OS DESC
Jan 25 00:00:00 runsvdir: [audio service]:audio service running
Jan 25 00:00:00 runsvdir: svcmgr: add_service('sys_cmd',4) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: svcmgr: add_service('rgn_cmd',1) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: svcmgr: add_service('vpss_cmd',2) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:00 runsvdir: [audio service]:AR_MPI_SYS_Init su
Jan 25 00:00:00 runsvdir: [audio service]:[audio]:create ipc channel success!
Jan 25 00:00:00 runsvdir: [audio service]:[audio]:added service sucess
Jan 25 00:00:00 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: HID hid_index 0 func_index 1
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: NCM ncm_index 0 func_index 2
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: ECM ecm_index 0 func_index 3
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: UAC uac1_index 0 func_index 4
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: HID hid_index 1 func_index 5
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: MTP mtp_index 0 func_index 6
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: MASS_STORAGE mass_storage_index 0 func_index 7
Jan 25 00:00:00 runsvdir: shell config usb0
Jan 25 00:00:00 runsvdir: Run storage service.
Jan 25 00:00:00 runsvdir: Run usb_gadget service.
Jan 25 00:00:00 runsvdir: Property sys.bsp.ncm set to true
Jan 25 00:00:00 runsvdir: shell config usb1
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_gadget_dir:42: usb_gadget_dir:/sys/kernel/config/usb_gadget/g0
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_udc_dir:61: udc_dir:/sys/kernel/config/usb_gadget/g0/UDC
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_mtp_dir:73: mtp_dir:/sys/kernel/config/usb_gadget/g0/functions/ffs.mtp
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_ums_dir:86: ums_dir:/sys/kernel/config/usb_gadget/g0/functions/mass_storage.usb0
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_ums_dir:88: ums_lun_dir:/sys/kernel/config/usb_gadget/g0/functions/mass_storage.usb0/lun.0/file
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_config_dir:116: config_dir:/sys/kernel/config/usb_gadget/g0/configs/b.1
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_storage_func_dir:235: storage_func_dir:/sys/kernel/config/usb_gadget/g0/configs/b.1/f1
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c init_dir:351: product_name:XREAL One
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c init_dir:365: manufacture_name:XREAL
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c init_dir:368: format_cmd:/bin/exfat/mkfs.exfat /dev/mmcblk0p22 -n "XREAL"
Jan 25 00:00:00 runsvdir: [usb_gadget_service] DEBUG usb_gadget.c get_usb_gadget_dir:85: usb_gadget_dir:/sys/kernel/config/usb_gadget/g0
Jan 25 00:00:00 runsvdir: [usb_gadget_service] DEBUG usb_gadget.c get_udc_dir:104: udc_dir:/sys/kernel/config/usb_gadget/g0/UDC
Jan 25 00:00:00 runsvdir: [usb_gadget_service] DEBUG usb_gadget.c get_config_dir:132: config_dir:/sys/kernel/config/usb_gadget/g0/configs/b.1
Jan 25 00:00:00 runsvdir: Property sys.bsp.ecm set to true
Jan 25 00:00:00 runsvdir: =======++++++++++++++++++===========
Jan 25 00:00:00 runsvdir: true true
Jan 25 00:00:00 runsvdir: =======++++++++++++++++++===========
Jan 25 00:00:00 runsvdir: =======++++++++++++++++++===========
Jan 25 00:00:00 runsvdir: start dhcpd
Jan 25 00:00:00 runsvdir: =======++++++++++++++++++===========
Jan 25 00:00:00 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: finished ...
Jan 25 00:00:00 runsvdir: Run Dhcp service.
Jan 25 00:00:00 runsvdir: svcmgr: add_service('sys_cmd',7) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: svcmgr: add_service('rgn_cmd',4) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: svcmgr: add_service('vpss_cmd',1) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: Internet Systems Consortium DHCP Server 4.4.3-P1
Jan 25 00:00:00 runsvdir: Copyright 2004-2022 Internet Systems Consortium.
Jan 25 00:00:00 runsvdir: All rights reserved.
Jan 25 00:00:00 runsvdir: For info, please visit https://www.isc.org/software/dhcp/
Jan 25 00:00:00 runsvdir: Config file: /etc/dhcpd.conf
Jan 25 00:00:00 runsvdir: Database file: /tmp/dhcpd.lease
Jan 25 00:00:00 runsvdir: PID file: /tmp/dhcpd.pid
Jan 25 00:00:00 runsvdir: Wrote 0 leases to leases file.
Jan 25 00:00:00 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.822800] [Scam]: (133 main)display device get oled opt arg:0
Jan 25 00:00:00 runsvdir: ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.823413] [Scam]: (149 main)--- media service start --- ^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.823421] [Scam]: (151 main)Initalize common vin source.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.827704] [Scam]: (74 vin_dp_camera_common_init)u64BlkSize[0]:4677632 u32BlkCnt[0]:12 u64BlkSize[1]:395264 u32BlkCnt[1]:5 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.827879] [Scam]: (797 XR_COMM_SYS_Init)camera vb pool is already configed.^[[0m
Jan 25 00:00:00 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.833381] [Scam]: (82 vin_dp_camera_common_init)SS-COMMON-0: config vb success!^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.915552] [Scam]: (103 vin_dp_camera_common_init)SS-COMMON-1: config dev colock success!^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.915588] [Scam]: (159 main)Creat thread: thread_display_service. device:lvds @5lane2^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.915634] [Scam]: (161 main)Creat thread: thread_camera_service.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.915720] [Scam]: (5179 camera_service_thread)ss0. Creat and init camera sevice context.^[[0m
Jan 25 00:00:00 runsvdir: DisplayService I : <timestamp boot up> -- [ 1.367 ]
Jan 25 00:00:00 runsvdir: DisplayService I : we will run display service (lvds@5lanesx2)
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916141] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<0> get vb pool id[0][1][2] = [-1][-1][-1]^[[0m
Jan 25 00:00:00 runsvdir: DisplayService I : <------- init display service ------->
Jan 25 00:00:00 runsvdir: read vb config data index:1 success.
Jan 25 00:00:00 runsvdir: read vb config data index:4 success.
Jan 25 00:00:00 runsvdir: DisplayService I : config pixel format YUV420p
Jan 25 00:00:00 runsvdir: DisplayService I : use 5lane2 port
Jan 25 00:00:00 runsvdir: DisplayService I : vo0 port index = 0x03
Jan 25 00:00:00 runsvdir: DisplayService I : use 5lane2 port
Jan 25 00:00:00 runsvdir: DisplayService I : vo1 port index = 0x0c
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916405] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<1> get vb pool id[0][1][2] = [1][7][-1]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:1 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916447] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<2> get vb pool id[0][1][2] = [1][7][-1]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916476] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<3> get vb pool id[0][1][2] = [2][7][-1]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916505] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<4> get vb pool id[0][1][2] = [2][7][-1]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916536] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<5> get vb pool id[0][1][2] = [2][7][-1]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:3 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916575] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<6> get vb pool id[0][1][2] = [2][3][7]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:3 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916613] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<7> get vb pool id[0][1][2] = [2][3][7]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:3 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916651] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<8> get vb pool id[0][1][2] = [2][3][7]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:3 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.916681] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<9> get vb pool id[0][1][2] = [3][7][-1]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.918267] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<10> get vb pool id[0][1][2] = [2][7][-1]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.918345] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<11> get vb pool id[0][1][2] = [2][7][-1]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[Listening on LPF/usb1/fc:d2:b6:ad:cc:6b/169.254.1.0/24
Jan 25 00:00:00 runsvdir: Sending on   LPF/usb1/fc:d2:b6:ad:cc:6b/169.254.1.0/24
Jan 25 00:00:00 runsvdir: 36;22mI/[25 00:00:00.918375] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<12> get vb pool id[0][1][2] = [2][7][-1]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:3 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.918471] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<13> get vb pool id[0][1][2] = [2][3][7]^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:2 success.
Jan 25 00:00:00 runsvdir: read vb config data index:3 success.
Jan 25 00:00:00 runsvdir: read vb config data index:7 success.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.918523] [Scam]: (685 first_init_imx681_stream_vin_config)stream type<14> get vb pool id[0][1][2] = [2][3][7]^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.921642] [Scam]: (5189 camera_service_thread)--- Camera service --- ^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.921685] [Scam]: (5190 camera_service_thread)ss1. Creat ipc^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.921869] [Scam]: (5198 camera_service_thread)ss1.1 set binder use single thread^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.921938] [Scam]: (5201 camera_service_thread)ss2. add <cameraservice> to ipc^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.922284] [Scam]: (5209 camera_service_thread)ss3. initCameraService: init queues^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.922306] [Scam]: (3629 initCameraService)Start initCameraService^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.922582] [Scam]: (3534 initCameraServiceVideoBufferQueue)rgb_pubrQueue: 0x7f680113a0^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.922595] [Scam]: (3535 initCameraServiceVideoBufferQueue)rgb_consQueue : 0x7f68011790^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.922601] [Scam]: (3536 initCameraServiceVideoBufferQueue)rgb_inusedQueue : 0x7f68011b80^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.922606] [Scam]: (3537 initCameraServiceVideoBufferQueue)rgb_gdcQueue : 0x7f68011f70^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.922611] [Scam]: (3538 initCameraServiceVideoBufferQueue)rgb_gdcQBuffer:(nil)^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.922627] [Scam]: (3545 initCameraServiceVideoBufferQueue)gray_pubrQueue: 0x7f68012360^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.922633] [Scam]: (3546 initCameraServiceVideoBufferQueue)gray_consQueue : 0x7f68012750^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.922638] [Scam]: (3547 initCameraServiceVideoBufferQueue)gray_inusedQueue : 0x7f68012b40^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.923452] [Scam]: (5213 camera_service_thread)ss4:Creat rgb cycleLifeManager and gray cycleLifeManager^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.923552] [Scam]: (5215 camera_service_thread)ss5:Creat rgb cycleLifeManager and gray cycleLifeManager^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.923593] [Scam]: (5217 camera_service_thread)ss6:Creat rgb thread_camera_plug_detect thread^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.923641] [Scam]: (5101 rgb_cycleLifeManager)ss4-1: creat rgb_publisher_looper and rgb_consumer_looper thread^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.923669] [Scam]: (5219 camera_service_thread)ss6+1:Creat imx681_exp_timestamp_proc_thread thread^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.923751] [Scam]: (5221 camera_service_thread)ss6+2:Creat thread_binder_message_process thread^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.923771] [Scam]: (5152 gray_cycleLifeManager)wait for gray client connect ...^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.923802] [Scam]: (5223 camera_service_thread)ss6+3:Creat thread_binder_message_process thread^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.923838] [Scam]: (5225 camera_service_thread)ss6+4:Creat thread_binder_message_process thread^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.923868] [Scam]: (5113 rgb_cycleLifeManager)wait for rgb client connect ...^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924011] [Scam]: (3268 gray_publisher_looper)---- gray_publisher_looper start ----^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924042] [Scam]: (2431 rgb_consumer_looper)debug--rgb_consumer_looper start^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924067] [CAM_IIC]: (179 camera_iic_acquire)open camera-2 i2c fd(16) slave addr(16) ok
Jan 25 00:00:00 runsvdir: ^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924084] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 240 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924101] [Scam]: (70 imx681_plug_in_out_proc_thread)Detect camera plug action !^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924111] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 240 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924119] [Scam]: (70 imx681_plug_in_out_proc_thread)Detect camera plug action !^[[0m
Jan 25 00:00:00 runsvdir: ^[[32binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: ;22mD/[25 00:00:00.924127] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924160] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 1, counter is 1.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924170] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924178] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 2, counter is 2.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924187] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924195] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 3, counter is 3.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924203] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924211] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 4, counter is 4.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924219] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924227] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 5, counter is 5.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924236] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924243] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 6, counter is 6.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924252] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924260] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 7, counter is 7.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924268] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924276] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 8, counter is 8.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924285] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 255 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924292] [Scam]: (75 imx681_plug_in_out_proc_thread)gpio level sum is 8, counter is 8.^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924301] [Scam]: (80 imx681_plug_in_out_proc_thread)Detect camera plug in action !^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924310] [Scam]: (2926 rgb_eis_gdc_process_thread)rgb publisher looper, pipe 0 channel 0^[[0m
Jan 25 00:00:00 runsvdir: read vb config data index:6 success.
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924381] [Scam]: (2933 rgb_eis_gdc_process_thread)eis_gdc_process_thread get gdc vb pool:6^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924393] [Scam]: (2706 rgb_publisher_looper)rgb publisher looper, pipe 0 channel 0^[[0m
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.924407] [Scam]: (96 imx681_image_process_thread) Init device type 2 i2c success.^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924533] [Scam]: (3436 camera_get_ebd_data_thread)camera get ebd data looper, pipe 0 channel 0^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.924561] [Scam]: (96 binder_message_dequeue)binder_message_dequeue,  q->count:0 ^[[0m
Jan 25 00:00:00 runsvdir: ^[[33;22mW/[25 00:00:00.924575] [Scam]: (100 binder_message_dequeue)binder_message_dequeue is empty, waitting ...^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.929106] [Scam]: (2534 gray_consumer_looper)---- gray_consumer_looper start ----^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.929147] [Scam]: (2540 gray_consumer_looper)debug--gray_consumer_looper start^[[0m
Jan 25 00:00:00 runsvdir: DisplayService I : get product_code:GF
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.932655] [Scam]: (1915 camera_buffer_debug_info_thread)rgb_queue_info_en: 0^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.932701] [Scam]: (1922 camera_buffer_debug_info_thread)gray_queue_info_en: 0^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.932709] [Scam]: (1929 camera_buffer_debug_info_thread)rgb_node_timestmp_save_to_file: 0^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.932716] [Scam]: (1936 camera_buffer_debug_info_thread)rgb_log_enable: 0^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.932723] [Scam]: (1943 camera_buffer_debug_info_thread)gray_log_enable: 0^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.932729] [Scam]: (1947 camera_buffer_debug_info_thread)rgb_queue_info_print:0 gray_queue_info_en:0 queueNode_timestamp_record:0 rgb_debug_log_enabl^[[0;31mDisplayService E: parse_default_config_file[499] 
Jan 25 00:00:00 runsvdir: ^[[0;31mDisplayService E: screen_probe[545] Failed to parse and update screen default config.^[[0;39m
Jan 25 00:00:00 runsvdir: e:0 gray_log_enable:0 ^[[0m
Jan 25 00:00:00 runsvdir: DisplayOLED I : Detected OLED model name (right): ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : Detected OLED model name (left): ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : screen fds opened successfully.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.934622] [CAM_IIC]: (179 camera_iic_acquire)open camera-3 i2c fd(19) slave addr(80) ok
Jan 25 00:00:00 runsvdir: ^[[0m
Jan 25 00:00:00 runsvdir: DisplayOLED I : screen soft reset in probe successfully!
Jan 25 00:00:00 runsvdir: DisplayOLED I : Left OLED model name: ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : Right OLED model name: ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : Screen name updated to: ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : Calibrated Data Version: V1
Jan 25 00:00:00 runsvdir: DisplayOLED I : Orbit Horizon Data: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Orbit Vertical Data: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Orbit Horizon Data: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Orbit Vertical Data: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[0]: 127
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[1]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[2]: 26
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[3]: 29
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[4]: 23
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[5]: 14
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[6]: 124
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[7]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[8]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[9]: 31
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[10]: 37
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[11]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[12]: 17
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[13]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[14]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[15]: 49
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[16]: 55
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[17]: 59
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[18]: 51
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[19]: 35
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[20]: 9
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[0]: 127
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[1]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[2]: 35
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[3]: 52
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[4]: 50
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[5]: 45
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[6]: 18
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[7]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[8]: 30
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[9]: 43
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[10]: 68
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[11]: 65
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[12]: 56
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[13]: 26
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[14]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[15]: 58
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[16]: 77
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[17]: 94
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[18]: 90
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[19]: 82
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[20]: 41
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[0]: 127
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[1]: 22
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[2]: 16
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[3]: 15
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[4]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[5]: 249
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[6]: 124
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[7]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[8]: 24
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[9]: 20
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[10]: 21
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[11]: 10
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[12]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[13]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[14]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[15]: 41
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[16]: 41
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[17]: 45
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[18]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[19]: 18
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[20]: 9
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[0]: 127
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[1]: 22
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[2]: 25
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[3]: 35
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[4]: 34
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[5]: 30
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[6]: 13
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[7]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[8]: 25
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[9]: 32
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[10]: 49
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[11]: 46
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brigbinder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: Listening on LPF/usb0/fc:d2:b6:ad:cc:6a/169.254.2.0/24
Jan 25 00:00:00 runsvdir: Sending on   LPF/usb0/fc:d2:b6:ad:cc:6a/169.254.2.0/24
Jan 25 00:00:00 runsvdir: Sending on   Socket/fallback/fallback-net
Jan 25 00:00:00 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:00 runsvdir: htness[12]: 39
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[13]: 17
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[14]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[15]: 46
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[16]: 64
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[17]: 75
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[18]: 72
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[19]: 65
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[20]: 32
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[0]: -40, Y[0]: -45
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[1]: -16, Y[1]: -16
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[2]: -18, Y[2]: -18
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[3]: -19, Y[3]: -21
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[4]: -21, Y[4]: -22
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[5]: -19, Y[5]: -21
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[6]: -20, Y[6]: -20
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[7]: -20, Y[7]: -21
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[8]: -20, Y[8]: -20
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[9]: -20, Y[9]: -20
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[0]: -34, Y[0]: -47
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[1]: -12, Y[1]: -19
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[2]: -16, Y[2]: -22
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[3]: -16, Y[3]: -24
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[4]: -18, Y[4]: -25
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[5]: -18, Y[5]: -24
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[6]: -18, Y[6]: -23
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[7]: -18, Y[7]: -23
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[8]: -18, Y[8]: -22
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[9]: -20, Y[9]: -23
Jan 25 00:00:00 runsvdir: DisplayOLED I : oled_calibrated_data is ok
Jan 25 00:00:00 runsvdir: DisplayOLED I : Successfully parsed and updated OLED calibration data.
Jan 25 00:00:00 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:00:00 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:00:00 runsvdir: DisplayOLED I : Set ecx34x_screen brightness:122.
Jan 25 00:00:00 runsvdir: DisplayOLED I : Set ecx34x_screen duty:95.
Jan 25 00:00:00 runsvdir: DisplayOLED I : Set oled_left x:-13,y:-18.
Jan 25 00:00:00 runsvdir: DisplayOLED I : Set oled_right x:-13,y:-18.
Jan 25 00:00:00 runsvdir: DisplayOLED I : screen set config parameters successfully!
Jan 25 00:00:00 runsvdir: DisplayService I : start task_main thread
Jan 25 00:00:00 runsvdir: DisplayService I : dp_rx_get_traning_status not support now
Jan 25 00:00:00 runsvdir: DisplayService I : get training status failed
Jan 25 00:00:00 runsvdir: DisplayService I : dp monitor start
Jan 25 00:00:00 runsvdir: DisplayService I : dp_rx_get_hdcp_status not support now
Jan 25 00:00:00 runsvdir: DisplayService I : get hdcp status failed
Jan 25 00:00:01 runsvdir: DisplayService I : run send edid thread
Jan 25 00:00:01 runsvdir: DisplayService I : run dpisp sate machine, current_state=0
Jan 25 00:00:01 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:01 runsvdir: DisplayService I : Get frame stats: Successes 0.00, Failures 0.00
Jan 25 00:00:01 runsvdir: DisplayService I : dpvsync : 0.00
Jan 25 00:00:01 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:01 runsvdir: DisplayService D : wait for client
Jan 25 00:00:01 runsvdir: DisplayService I : -- RUN COMMAND THREAD --
Jan 25 00:00:01 runsvdir: DisplayService I : debug-command listen...
Jan 25 00:00:01 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f77e0e108
Jan 25 00:00:01 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:01 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:00:01 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:01 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:01 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:00:01 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:01 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:01 runsvdir: DisplayService I : stSnsSize.u32Width = 1920
Jan 25 00:00:01 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:01 runsvdir: DisplayService I : stTiming.hblank = 280
Jan 25 00:00:01 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:01 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 1920
Jan 25 00:00:01 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:01 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:01 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 0.000000
Jan 25 00:00:01 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:00:01 runsvdir: read vb config data index:4 success.
Jan 25 00:00:01 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:00:01 runsvdir: read vb config data index:4 success.
Jan 25 00:00:01 runsvdir: get vb pool id <4>!
Jan 25 00:00:01 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:00:01 runsvdir: DisplayService I : getpro capture_vio_key: false
Jan 25 00:00:01 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:00:01 runsvdir: The checksum successfully matched, checksum_calc=0x51, checksum_read=0x51
Jan 25 00:00:01 runsvdir: json file len:447^[[36;22mI/[25 00:00:00.999169] [CAM_IIC]: (196 camera_iic_release)Close camera-3 i2c fd sucess.
Jan 25 00:00:01 runsvdir: ^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:00.999474] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 240 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:00.999486] [Scam]: (70 imx681_plug_in_out_proc_thread)Detect binder: BR_SPAWN_LOOPER
Jan 25 00:00:01 runsvdir: [audio service]:[audio]:handleClientRequest cmd[1]
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: [audio service]:[audio]:handleClientRequest cmd[7]
Jan 25 00:00:01 runsvdir: [audio service]:[audio binder service]:GET_CURRENT_MODE GET_CURRENT_MODE[0]
Jan 25 00:00:01 runsvdir: camera plug action !^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:00.999512] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 240 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:00.999518] [Scam]: (70 imx681_plug_in_out_proc_thread)Detect camera plug action !^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:00.999524] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:00.999529] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 1, counter is 1.^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:00.999536] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:00.999541] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 2, counter is 2.^[[0m
Jan 25 00:00:01 runsvdir: DisplayService I : <timestamp init vi done> -- [ 1.457 ]
Jan 25 00:00:01 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.589574] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:01.590010] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 3, counter is 3.^[[0m
Jan 25 00:00:01 runsvdir: DisplayService I : __handleClientRequest[1508] recv data: connect service 
Jan 25 00:00:01 runsvdir: DisplayService I : REGISTER_CALLBACK, callback - displayservice_callback
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.138930] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:01.138951] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 4, counter is 4.^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:01.146085] [CAM_IIC]: (179 camera_iic_acquire)open camera-3 i2c fd(19) slave addr(80) ok
Jan 25 00:00:01 runsvdir: ^[[0m
Jan 25 00:00:01 runsvdir: The checksum successfully matched, checksum_calc=0xff, checksum_read=0xff
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:01.152141] [CAM_IIC]: (196 camera_iic_release)Close camera-3 i2c fd sucess.
Jan 25 00:00:01 runsvdir: ^[[0m
Jan 25 00:00:01 runsvdir: ^[[31;22mE/[25 00:00:01.152148] [Scam]: (4018 handleClientRequest)camera_custom_sn_read success, SN:<A1#Z1X655T11861JD>^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.152208] [Scam]: (70 binder_message_enqueue)cmd enqueue,  q->count:0 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.152221] [Scam]: (82 binder_message_enqueue)message queue debug------>>> 01, q->rear:0^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.152235] [Scam]: (88 binder_message_enqueue)message queue debug------>>> 02^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.152252] [Scam]: (4277 binder_message_process_thread)recive client cmd DISCONNECT^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.152264] [Scam]: (96 binder_message_dequeue)binder_message_dequeue,  q->count:0 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[33;22mW/[25 00:00:01.152270] [Scam]: (100 binder_message_dequeue)binder_message_dequeue is empty, waitting ...^[[0m
Jan 25 00:00:01 runsvdir: DisplayService I : <timestamp get a video change interrupt> -- [ 1.635 ]
Jan 25 00:00:01 runsvdir: DisplayService I : pixel clock: 222750000, fps = 90
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.208918] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:01.208961] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 5, counter is 5.^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.278926] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:01.278956] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 6, counter is 6.^[[0m
Jan 25 00:00:01 runsvdir: ^[[32;22mD/[25 00:00:01.348916] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:01 runsvdir: ^[[36;22mI/[25 00:00:01.348959] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 7, counter is 7.^[[0m
Jan 25 00:00:01 runsvdir: DisplayService I : <timestamp DP status changed start> -- [ 1.835 ]
Jan 25 00:00:01 runsvdir: DisplayService I : dp status changed, 0x0@0 -> 1920x1080@90
Jan 25 00:00:01 runsvdir: DisplayService I : <timestamp DP status changed end> -- [ 1.835 ]
Jan 25 00:00:01 runsvdir: DisplayService I : notify dp info update
Jan 25 00:00:01 runsvdir: DisplayService I : get a dp node, state 1
Jan 25 00:00:01 runsvdir: DisplayService I : should init mppvi
Jan 25 00:00:01 runsvdir: DisplayService I : broadcast dp event : 1
Jan 25 00:00:01 runsvdir: DisplayService I : release this event
Jan 25 00:00:01 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:01 runsvdir: DisplayService I : current vo's refresh rate 90Hz, return 3
Jan 25 00:00:01 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:01 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:01 runsvdir: DisplayService I : current vo's refresh rate 90Hz, return 3
Jan 25 00:00:01 runsvdir: DisplayService I : get edid 9,[audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:78: Retrying to get UsbGadgetService
Jan 25 00:00:01 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:125: Received string: get config
Jan 25 00:00:01 runsvdir: [usb_gadget_service] ERROR Service.cpp parseUsbGadgetConfig:75: Invalid config segment: get config
Jan 25 00:00:01 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:135: usb gadget no change
Jan 25 00:00:01 runsvdir: [usb_gadget_client] DEBUG nr_usb_gadget.cpp get_usb_gadget_config:230: Configuration get: ncm=enable,ecm=enable,hid=enable,uac=enable
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.558] [common/common.c] [get_process_state-1089] : process dhcpd status : S
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.558] [common/common.c] [start_process_by_name-650] : process dhcpd is running
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.558] [command_service.c] [create_task-199] : prepare to create thread : 0, name : hid_data_process
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [command_service.c] [create_task-199] : prepare to create thread : 1, name : tcp_data_process
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [command_service.c] [create_task-199] : prepare to create thread : 2, name : hid_data_receive
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [sub_thread/hid_receive_send_thread.c] [hid_data_receive_thread-118] : start
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [command_service.c] [create_task-199] : prepare to create thread : 3, name : hid_data_send
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [command_service.c] [create_task-199] : prepare to create thread : 4, name : restart_control
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [command_service.c] [create_task-199] : prepare to create thread : 5, name : tcp_data_receive
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [sub_thread/tcp_receive_send_thread.c] [tcp_data_receive_thread-26] : waiting connection...
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [command_service.c] [create_task-199] : prepare to create thread : 6, name : tcp_data_send
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [command_service.c] [create_task-199] : prepare to create thread : 7, name : dsp_upgrade
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [command_service.c] [create_task-199] : prepare to create thread : 8, name : upgrade_monitor
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.559] [command_service.c] [create_task-199] : prepare to create thread : 9, name : usb_config
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.560] [hardware/DSP_IA8201/src/dsp_io_ctl.c] [dsp_io_init-23] : ret : 0, mode : 1
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.560] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-30] : tty path : /sys/devices/platform/soc/1504000.serial/tty
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.560] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-37] : .
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.560] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-37] : ..
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.560] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-37] : ttyS2
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.560] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-42] : name : ttyS2
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.560] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-46] : get uart node : /dev/ttyS2
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.563] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-54] : serial irq : 32
Jan 25 00:00:01 runsvdir: sh: can't create /proc/irq/32/smp_affinity: nonexistent directory
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.566] [hardware/DSP_IA8201/src/dsp_mode_set.c] [get_dsp_sysmode-810] : read mode : 2
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.566] [command_service.c] [create_task-199] : prepare to create thread : 10, name : cmd_audio
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.566] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.509_USERROOT
Jan 25 00:00:01 runsvdir: svcmgr: add_service('sys_cmd',c) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.566] [command_service.c] [set_property_values-724] : version code str : 2509, version code : 2509
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.567] [function/upgrade_function.c] [read_dsp_version-1046] : lock dsp
Jan 25 00:00:01 runsvdir: svcmgr: add_service('rgn_cmd',7) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:01 runsvdir: svcmgr: add_service('vpss_cmd',4) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.568] [function/upgrade_function.c] [read_dsp_version-1052] : dsp version : 15.A.00.069_20241211
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.568] [common/common.c] [update_property_file-1314] : key offset [0], value offset [20] eof offset [41]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.568] [common/common.c] [update_property_file-1338] : file [/persist/device.prop] key [sys.bsp.dsp_version] value is the same as setting [15.A.00.069_20241211]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.568] [common/common.c] [update_property_file-1314] : key offset [41], value offset [60] eof offset [81]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.569] [common/common.c] [update_property_file-1338] : file [/persist/device.prop] key [ro.bsp.dsp_version] value is the same as setting [15.A.00.069_20241211]
Jan 25 00:00:01 runsvdir: libc: Unable to set property "ro.bsp.dsp_version" to "15.A.00.069_20241211": error code: 0xb
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.569] [common/common.c] [update_property_file-1392] : set property [ro.bsp.dsp_version]=[15.A.00.069_20241211] failed
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.569] [upgrade/upgrade.c] [set_upgrade_progress-166] : [0 0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.569] [upgrade/upgrade.c] [set_upgrade_status-156] : set upgrade status : false
Jan 25 00:00:01 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1548] get frame failed^[[0;39m
Jan 25 00:00:01 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:01 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:01 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:01 runsvdir: ar_axi_dma_signal_semaphore_init success!
Jan 25 00:00:01 runsvdir: cjson_buffer is null ^M
Jan 25 00:00:01 runsvdir: vin start @ sdk version [5hJk2Ld8Rt9sFpQw] 
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390381][0298][0x7f76dad200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3693 load vin driver start^[[0m
Jan 25 00:00:01 runsvdir: ^[[31;22m[124390387][0298][0x7f76dad200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390388][0298][0x7f76dad200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3957 load vin driver end max_sensor_count=5^[[0m
Jan 25 00:00:01 runsvdir: Create socked success
Jan 25 00:00:01 runsvdir: Bind socket success
Jan 25 00:00:01 runsvdir: Listen socket success
Jan 25 00:00:01 runsvdir: client accept thread running
Jan 25 00:00:01 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:01 runsvdir: ^[[31;22m[25 00:00:00.976083][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:01 runsvdir: ^[[31;22m[25 00:00:00.976589][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:01 runsvdir: ^[[31;22m[25 00:00:00.977792][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:01 runsvdir: ^[[31;22m[25 00:00:00.977841][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390395][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390395][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390395][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390396][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390396][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390396][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390396][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 runsvdir: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 runsvdir: ^[[31;22m[124390396][0298][0x7f760f7200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 runsvdir: 358e7000 35b05000 35b8e000 0 0 0 35c11000 35e2f000 35eb8000 0 0 0 
Jan 25 00:00:01 runsvdir: aec index -1589366767 line 1125 gain 1.000000 
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390396][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390396][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390396][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390398][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:01 runsvdir: ^[[33;22m[124390455][0298][0x7f760b5200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:01 runsvdir: ^[[31;22m[124390455][0298][0x7f760b5200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124390455][0298][0x7f7584f200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285
Jan 25 00:00:01 runsvdir: ^[[33;22m[124390455][0298][0x7f76118200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390455][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390456][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390456][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390456][0298][0x7f76094[audio service]:ada checked 1
Jan 25 00:00:01 runsvdir: [audio service]:ada checked done
Jan 25 00:00:01 runsvdir: get capture mask: 3
Jan 25 00:00:01 runsvdir: [audio service]:dp_audio_only:0
Jan 25 00:00:01 runsvdir: [audio service]:uac vb pool is already configed.
Jan 25 00:00:01 runsvdir: read vb config data index:0 success.
Jan 25 00:00:01 runsvdir: [audio service]:Uac get vb pool id [0] success!
Jan 25 00:00:01 runsvdir: [audio service]:start UAC
Jan 25 00:00:01 runsvdir: [audio service]:buffer malloc success size[4096]!
Jan 25 00:00:01 runsvdir: get playback mask: 3
Jan 25 00:00:01 runsvdir: [audio service]:AR_MPI_AO_GetPubAttr enSamplerate:48000
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ao_send run
Jan 25 00:00:01 runsvdir: [audio service]:buffer_size 1024
Jan 25 00:00:01 runsvdir: [audio service]:period_size 256
Jan 25 00:00:01 runsvdir: [audio service]:app_ring_buffer_readn buff_size[2048] s32Ret[0]
Jan 25 00:00:01 runsvdir: [audio service]:latency=10666
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ao run
Jan 25 00:00:01 runsvdir: [audio service]:snd_ctl_volume_thread[0] run 
Jan 25 00:00:01 runsvdir: [audio service]:HW info suc: 0
Jan 25 00:00:01 runsvdir: [audio service]:  card - 0
Jan 25 00:00:01 runsvdir: [audio service]:  id - 'UAC1Gadget'
Jan 25 00:00:01 runsvdir: [audio service]:  driver - 'UAC1_Gadget'
Jan 25 00:00:01 runsvdir: [audio service]:  name - 'UAC1_Gadget'
Jan 25 00:00:01 runsvdir: [audio service]:  longname - 'UAC1_Gadget 0'
Jan 25 00:00:01 runsvdir: [audio service]:  mixername - 'UAC1_Gadget'
Jan 25 00:00:01 runsvdir: [audio service]:  components - ''
Jan 25 00:00:01 runsvdir: [audio service]:elem list count=7
Jan 25 00:00:01 runsvdir: [audio service]:numid=1, name='Playback Pitch 1000000'
Jan 25 00:00:01 runsvdir: [audio service]:numid=2, name='PCM Playback Switch'
Jan 25 00:00:01 runsvdir: [audio service]:numid=3, name='PCM Playback Volume'
Jan 25 00:00:01 runsvdir: [audio service]:numid=4, name='Playback Rate'
Jan 25 00:00:01 runsvdir: [audio service]:numid=5, name='PCM Capture Switch'
Jan 25 00:00:01 runsvdir: [audio service]:numid=6, name='PCM Capture Volume'
Jan 25 00:00:01 runsvdir: [audio service]:numid=7, name='Capture Rate'
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :5 1
Jan 25 00:00:01 runsvdir: [audio service]:open /dev/smartPA_L su
Jan 25 00:00:01 runsvdir: [audio service]:open /dev/smartPA_R su
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ai run
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ai Thread is paused...
Jan 25 00:00:01 runsvdir: [audio service]:smartPA start su
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :7 0
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :6 0
Jan 25 00:00:01 runsvdir: [audio service]:hsv[0],index[0]dspv[1000]
Jan 25 00:00:01 runsvdir: [audio service]:set_dsp_volume open pipe file failed -1 -1 No such device or address
Jan 25 00:00:01 runsvdir: [audio service]:volume changing , volume is:1000 0
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :4 0
Jan 25 00:00:01 runsvdir: [audio service]:dsp_mode_without_mic :1
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.634] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-472] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.646] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-509] : set mode [1], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.646] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-126] : set dsp mode back over : 0
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.696] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.696] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [1], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.696] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 1
Jan 25 00:00:01 runsvdir: [audio service]:read_dsp_characteristic reg[1][0] length[1]
Jan 25 00:00:01 runsvdir: [audio service]:set_dsp_mode result[0]
Jan 25 00:00:01 runsvdir: [audio service]:dsp mode set to 1
Jan 25 00:00:01 runsvdir: [audio service]:snd_ctl_event wait event
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :7 0
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :4 48000
Jan 25 00:00:01 runsvdir: [audio service]:dsp_mode_without_mic :1
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.747] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-472] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.759] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-509] : set mode [3], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.759] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-126] : set dsp mode back over : 0
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.809] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.809] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [1], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.809] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 1
Jan 25 00:00:01 runsvdir: [audio service]:read_dsp_characteristic reg[1][0] length[1]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.860] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.860] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [3], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.860] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 3
Jan 25 00:00:01 runsvdir: [audio service]:read_dsp_characteristic reg[3][0] length[1]
Jan 25 00:00:01 runsvdir: [audio service]:set_dsp_mode result[0]
Jan 25 00:00:01 runsvdir: [audio service]:dsp mode set 3
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :4 0
Jan 25 00:00:01 runsvdir: [audio service]:dsp_mode_without_mic :1
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ai Thread is paused...
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.910] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-472] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.922] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-509] : set mode [1], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.923] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-126] : set dsp mode back over : 0
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.973] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.973] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [3], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.973] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 3
Jan 25 00:00:01 runsvdir: [audio service]:read_dsp_characteristic reg[3][0] length[1]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.023] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.024] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [1], return [0]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.024] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 1
Jan 25 00:00:02 runsvdir: [audio service]:read_dsp_characteristic reg[1][0] length[1]
Jan 25 00:00:02 runsvdir: [audio service]:set_dsp_mode result[0]
Jan 25 00:00:02 runsvdir: [audio service]:dsp mode set to 1
Jan 25 00:00:02 runsvdir: [audio service]:snd_numid_handle :6 0
Jan 25 00:00:02 runsvdir: [audio service]:hsv[0],index[0]dspv[1000]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.074] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-539] : setting volume 1000, register value 000be800
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.076] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-552] : set volume [1000], return [0]
Jan 25 00:00:02 runsvdir: [audio service]:[audio]:handleClientRequest cmd[4]
Jan 25 00:00:02 runsvdir: [audio service]:[audio]:REGISTER_CALLBACK, callback - audioService_
Jan 25 00:00:02 runsvdir: [audio service]:[audio]:register [audioService_] successfully, callbackHandle = 1
Jan 25 00:00:02 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.126] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-560] : try lock dsp
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.126] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-582] : read volume [1000], return [0]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.126] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-191] : get dsp volume back over : 1000
Jan 25 00:00:02 runsvdir: [audio service]:read_dsp_characteristic reg[e8][3] length[2]
Jan 25 00:00:02 runsvdir: [audio service]:set_dsp_volume result[0] volume[1000]
Jan 25 00:00:02 runsvdir: [audio service]:volume changing , volume is:1000 0
Jan 25 00:00:02 runsvdir: [audio service]:snd_numid_handle :3 0
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.176] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-539] : setting volume 1000, register value 000be800
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.178] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-552] : set volume [1000], return [0]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.228] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-560] : try lock dsp
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.229] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-582] : read volume [1000], return [0]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.229] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-191] : get dsp volume back over : 1000
Jan 25 00:00:02 runsvdir: [audio service]:read_dsp_characteristic reg[e8][3] length[2]
Jan 25 00:00:02 runsvdir: [audio service]:set_dsp_volume result[0] volume[1000]
Jan 25 00:00:02 runsvdir: [audio service]:dsp volume set su[1000]
Jan 25 00:00:02 runsvdir:  dp_audio 0
Jan 25 00:00:02 runsvdir: DisplayService W : dpisp is not runing, can't get filter
Jan 25 00:00:02 runsvdir: DisplayService I : current vo's refresh rate 90Hz, return 3
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:01.488919] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 1 ^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:01.488959] [Scam]: (195 imx681_plug_in_out_proc_thread)gpio level value is 1, sum is 8, counter is 8.^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:01.488971] [Scam]: (65 imx681_plug_in_out_proc_thread)read imx681_plug_in_out gpio level [0] : 255 ^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:01.488977] [Scam]: (75 imx681_plug_in_out_proc_thread)gpio level sum is 8, counter is 8.^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:01.488982] [Scam]: (80 imx681_plug_in_out_proc_thread)Detect camera plug in action !^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:01.499157] [CAM_IIC]: (179 camera_iic_acquire)open camera-3 i2c fd(19) slave addr(80) ok
Jan 25 00:00:02 runsvdir: ^[[0m
Jan 25 00:00:02 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:02 runsvdir: The checksum successfully matched, checksum_calc=0x51, checksum_read=0x51
Jan 25 00:00:02 runsvdir: json file len:447^[[36;22mI/[25 00:00:01.560521] [CAM_IIC]: (196 camera_iic_release)Close camera-3 i2c fd sucess.
Jan 25 00:00:02 runsvdir: ^[[0m
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp client connected> -- [ 2.16 ]
Jan 25 00:00:02 runsvdir: DisplayService I : added a client
Jan 25 00:00:02 runsvdir: DisplayService W : dpisp is not ready, it's going to init, current state = 1
Jan 25 00:00:02 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:00:02 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp init isp > -- [ 2.33 ]
Jan 25 00:00:02 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:00:02 runsvdir: DisplayService I : Set original LUT...
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp init isp done> -- [ 2.44 ]
Jan 25 00:00:02 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:01.923971] [Scam]: (5231 camera_service_thread)ss7:waitting rgb cycleLifeManager and gray cycleLifeManager finish.^[[0m
Jan 25 00:00:02 runsvdir: DisplayService I : get dp info 1920x1080@90
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.256795] [Scam]: (3861 handleClientRequest)REGISTER_CALLBACK, callback - grayClientServer^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.256893] [Scam]: (3902 handleClientRequest)register [grayClientServer] successfully, callbackHandle = 1^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257343] [Scam]: (70 binder_message_enqueue)cmd enqueue,  q->count:0 ^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257355] [Scam]: (82 binder_message_enqueue)message queue debug------>>> 01, q->rear:1^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257366] [Scam]: (88 binder_message_enqueue)message queue debug------>>> 02^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257381] [Scam]: (4250 binder_message_process_thread)recive client index:2 START_PREVIEW stream:1 cf50:1349086275^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257393] [Scam]: (4517 get_streamSwitchMethods)<0>>>>>>>>>>input[0 1] target[0 1] current steam:0 target stream:0^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257399] [Scam]: (4578 get_streamSwitchMethods)<1>>>>>>>>>>input[0 1] target[0 1] current steam:0 target stream:9^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257406] [Scam]: (131 get_stream_transition_method)search stream switch method from:0 to:9:^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257412] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=0, node stream type:0 target stream type:0^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257418] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=1, node stream type:0 target stream type:1^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257424] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=2, node stream type:0 target stream type:2^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257429] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=3, node stream type:0 target stream type:3^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257434] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=4, node stream type:0 target stream type:4^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257439] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=5, node stream type:0 target stream type:5^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257445] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=6, node stream type:0 target stream type:6^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257450] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=7, nsvcmgr: add_service('sys_cmd',f) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:02 runsvdir: svcmgr: add_service('vpss_cmd',c) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:02 runsvdir: ode stream type:0 target stream type:7^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257482] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=8, node stream type:0 target stream type:8^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257488] [Scam]: (140 get_stream_transition_method)search stream switch method current i=0 j=9, node stream type:0 target stream type:9^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257494] [Scam]: (4702 streamSwitch)switch steam method: 1^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257500] [Scam]: (4703 streamSwitch)step   operatFlag      operate     currentStream   targetStream^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257504] [Scam]: (4709 streamSwitch)[0]       1             1             0             9^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257510] [Scam]: (4709 streamSwitch)[1]       0             0             0             0^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257514] [Scam]: (4709 streamSwitch)[2]       0             0             0             0^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257519] [Scam]: (4709 streamSwitch)[3]       0             0             0             0^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.257527] [Scam]: (4666 updateStreamRunningStatus)update stream running status to 1^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257532] [Scam]: (4883 streamSwitch)Function set: crop[0 0 0] scal[0 0 0] distortion[0] eis[0] photo[0] cf50[0] ledSet[0]^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.257544] [Scam]: (4887 streamSwitch)xr_imx681VinConfig get: sensor stream type[9] ...^[[0m
Jan 25 00:00:02 runsvdir: read vb config data index:3 success.
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.258430] [Scam]: (9352 USER_Camera_Init)debug ---------> sensor stream type = 9 9 0x7f68007fc0 0x7f68007fc0^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.258460] [Scam]: (1584 xr_imx681_vin_init)INIT-S1: XR_COMM_GetSnsObj^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.265344] [Scam]: (82 XR_COMM_GetSnsObj)open sensor lib success, sensor name: imx681, enSnsType 9 
Jan 25 00:00:02 runsvdir: ^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.265387] [Scam]: (1594 xr_imx681_vin_init)INIT-S3: XR_COMM_SYS_Init_User^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.265425] [Scam]: (190 XR_COMM_SYS_Init_User)camera vb pool is already configed.^[[0m
Jan 25 00:00:02 runsvdir: read vb config data index:3 success.
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.269643] [Scam]: (245 XR_COMM_SYS_Init_User)stream type<9> get vb pool id[0][1] = [3][7]^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.269673] [Scam]: (1602 xr_imx681_vin_init)INIT-S4: XR_OpenDev_NoLoadDriver_With_LTMmode^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270260] [Scam]: (1623 xr_imx681_vin_init)INIT-S5: AR_MPI_VI_SetMipiBindDev^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270296] [Scam]: (1632 xr_imx681_vin_init)INIT-S6: AR_MPI_VI_SetComboDevAttr^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270307] [Scam]: (1639 xr_imx681_vin_init)INIT-S7: AR_MPI_VI_SetDevAttr^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270315] [Scam]: (1646 xr_imx681_vin_init)INIT-S8: AR_MPI_VI_EnableDev^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270516] [Scam]: (1653 xr_imx681_vin_init)INIT-S9: --- set pipe and channel attributes ---^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270524] [Scam]: (1660 xr_imx681_vin_init)INIT-S10.0: AR_MPI_VI_SetDevBindPipe^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270737] [Scam]: (1818 xr_imx681_vin_init)INIT-S14.0: AR_MPI_VI_SetChnAttr^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270748] [Scam]: (1829 xr_imx681_vin_init)INIT-S14+1.0: AR_MPI_VI_SetChnPoolId^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.270753] [Scam]: (1841 xr_imx681_vin_init)stream width[504] not align with 32 byte, set stride as default.^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270757] [Scam]: (1845 xr_imx681_vin_init)INIT-S15.0: AR_MPI_VI_SetChnPoolId^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.270790] [Scam]: (1853 xr_imx681_vin_init)vi pipe[0] channel[2] bind vb pool[3] success!^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270800] [Scam]: (1818 xr_imx681_vin_init)INIT-S14.1: AR_MPI_VI_SetChnAttr^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270808] [Scam]: (1829 xr_imx681_vin_init)INIT-S14+1.1: AR_MPI_VI_SetChnPoolId^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.270813] [Scam]: (1841 xr_imx681_vin_init)stream width[258] not align with 32 byte, set stride as default.^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.270817] [Scam]: (1845 xr_imx681_vin_init)INIT-S15.1: AR_MPI_VI_SetChnPoolId^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.270822] [Scam]: (1853 xr_imx681_vin_init)vi pipe[1] channel[2] bind vb pool[7] success!^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.270831] [Scam]: (1896 xr_imx681_vin_init)crop X:0 Y:0 W:504 H:37200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:02 runsvdir: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390456][0298][0x7f7584f200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:02 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390456][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:02 runsvdir: 35f41000 3615f000 361e8000 0 0 0 3626b000 36489000 36512000 0 0 0 
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390456][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:02 runsvdir: aec index -2059128784 line 1125 gain 1.000000 
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390456][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390456][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:02 runsvdir: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390457][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:02 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:02 runsvdir: 3659b000 367b9000 36842000 0 0 0 368c5000 36ae3000 36b6c000 0 0 0 
Jan 25 00:00:02 runsvdir: [ahb-3] start llp_pa:0x3bd2e000 llp:0x7f75728000!
Jan 25 00:00:02 runsvdir: [ahb-3] src:0x3bd26000 dest:0x13111c8 ctl_l:0x18909125!
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390458][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390458][0298][0x7f76118200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ao s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:02 runsvdir: ^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390459][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:02 runsvdir: [ahb-4] start llp_pa:0x3bd3f000 llp:0x7f756bc000!
Jan 25 00:00:02 runsvdir: [ahb-4] src:0x13111c0 dest:0x3bd2f000 ctl_l:0x1a209425!
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390459][0298][0x7f771d0200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ai s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:02 runsvdir: ^[[0m
Jan 25 00:00:02 runsvdir: open the driver sns:imx681 obj:stSnsimx681Obj lib:libsns_imx681.so 
Jan 25 00:00:02 runsvdir: ^[[31;22m[25 00:00:02.270558][0447][0x7f6cff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3554 !!!!!!!!!! Note:your app cfg the isp read and vif write  as no compress,this mode isp read and vif write will cost more dd
Jan 25 00:00:02 runsvdir: ^[[31;22m[25 00:00:02.270726][0447][0x7f6cff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3657 stream id 0, no need cfg the sns_var_info^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390524][0298][0x7f7584f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390528][0298][0x7f760f7200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390528][0298][0x7f760f7200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390528][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390529][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390530][0298][0x7f76118200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:02 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:02 runsvdir: ^[[31;22m[25 00:00:02.334874][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:02 runsvdir: ^[[31;22m[25 00:00:02.334904][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:02 runsvdir: ^[[31;22m[25 00:00:02.335014][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:02 runsvdir: ^[[31;22m[25 00:00:02.335024][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390531][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[1243908 ^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.271133] [Scam]: (1899 xr_imx681_vin_init)INIT-S13.0: AR_MPI_VIN_PipeBindSensor^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.271688] [Scam]: (1907 xr_imx681_vin_init)INIT-S17.0: AR_MPI_ISP_MemInit^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.271704] [Scam]: (1915 xr_imx681_vin_init)INIT-S18.0: AR_MPI_ISP_SetPubAttr^[[0m
Jan 25 00:00:02 runsvdir: cmos_set_wdr_mode: linear mode
Jan 25 00:00:02 runsvdir: cmos_set_image_mode:4
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.273209] [Scam]: (1923 xr_imx681_vin_init)INIT-S19.0: AR_MPI_VI_GetPipeExtAttr^[[0m
Jan 25 00:00:02 runsvdir: tuning file not exit, please check ...^[[32;22mD/[25 00:00:02.273310] [Scam]: (1945 xr_imx681_vin_init)INIT-S20.0: AR_MPI_VI_SetPipeExtAttr^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.273321] [Scam]: (1954 xr_imx681_vin_init)INIT-S21.0: AR_MPI_ISP_Init^[[0m
Jan 25 00:00:02 runsvdir: tuning file not exit, please check ...^[[32;22mD/[25 00:00:02.273344] [Scam]: (1961 xr_imx681_vin_init)INIT-S22.0: AR_MPI_ISP_Run^[[0m
Jan 25 00:00:02 runsvdir: DisplayService I : get dp info 1920x1080@90
Jan 25 00:00:02 runsvdir: open eeprom i2c fd(25) slave addr(80) ok
Jan 25 00:00:02 runsvdir: Camera_eeprom_iic_init ok.
Jan 25 00:00:02 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:00:02 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 2.758 ]
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 2.758 ]
Jan 25 00:00:02 runsvdir: IIC read addr:0x0066 success! res:2
Jan 25 00:00:02 runsvdir: read eeprom awb awb_calibrate_valid reg data:1
Jan 25 00:00:02 runsvdir: IIC read addr:0x006f success! res:2
Jan 25 00:00:02 runsvdir: read eeprom awb CAL reg data success:
Jan 25 00:00:02 runsvdir: IIC read addr:0x0067 success! res:2
Jan 25 00:00:02 runsvdir: read eeprom awb gold reg data success:
Jan 25 00:00:02 runsvdir: IIC read addr:0x009c success! res:2
Jan 25 00:00:02 runsvdir: read eeprom lsc_calibrate_valid reg data:1
Jan 25 00:00:02 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 2.778 ]
Jan 25 00:00:02 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f77e0e108
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 2.779 ]
Jan 25 00:00:02 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:00:02 runsvdir: IIC read addr:0x03bd success! res:2
Jan 25 00:00:02 runsvdir: read eeprom lsc calibrate reg len:2, data:
Jan 25 00:00:02 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:00:02 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:00:02 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:00:02 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:00:02 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:00:02 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:02 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:02 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f77e0e108
Jan 25 00:00:02 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:02 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:00:02 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:02 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:02 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:00:02 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:02 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:02 runsvdir: DisplayService I : stSnsSize.u32Width = 1920
Jan 25 00:00:02 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:02 runsvdir: DisplayService I : stTiming.hblank = 280
Jan 25 00:00:02 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:02 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 1920
Jan 25 00:00:02 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:02 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:02 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 0.000000
Jan 25 00:00:02 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:00:02 runsvdir: read vb config data index:4 success.
Jan 25 00:00:02 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:00:02 runsvdir: read vb config data index:4 success.
Jan 25 00:00:02 runsvdir: get vb pool id <4>!
Jan 25 00:00:02 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 2.792 ]
Jan 25 00:00:02 runsvdir: IIC read addr:0x009d success! res:2
Jan 25 00:00:02 runsvdir: read eeprom lsc gold reg len:2, data:
Jan 25 00:00:02 runsvdir: Close camera-0 i2c fd sucess.
Jan 25 00:00:02 runsvdir: Camera eeprom iic release ok.
Jan 25 00:00:02 runsvdir: IMX681: bInit[0] enWDRMode[0] u8ImgMode[4]
Jan 25 00:00:02 runsvdir: u8DevNum=4, /dev/i2c-4
Jan 25 00:00:02 runsvdir: ==============================================================
Jan 25 00:00:02 runsvdir: == SENSOR_SINGLE_STREAM_QVGA_504x378_30FPS_MODE 30fps inital ==
Jan 25 00:00:02 runsvdir: ==============================================================
Jan 25 00:00:02 runsvdir: imx681_cfg_ebd 256*2 
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.452015] [Scam]: (1969 xr_imx681_vin_init)INIT-S23.0: trigger AEC AWB^[[0m
Jan 25 00:00:02 runsvdir: ^[[36;22mI/[25 00:00:02.452100] [Scam]: (1896 xr_imx681_vin_init)crop X:0 Y:0 W:258 H:2 ^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.452106] [Scam]: (1899 xr_imx681_vin_init)INIT-S13.1: AR_MPI_VIN_PipeBindSensor^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.452182] [Scam]: (1907 xr_imx681_vin_init)INIT-S17.1: AR_MPI_ISP_MemInit^[[0m
Jan 25 00:00:02 runsvdir: ^[[32;22mD/[25 00:00:02.452193] [Scam]: (1915 xr_imx531][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390531][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390531][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390531][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390531][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390531][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:02 runsvdir: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390531][0298][0x7f7568a200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:02 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:02 runsvdir: 35f41000 3615f000 361e8000 0 0 0 3626b000 36489000 36512000 0 0 0 
Jan 25 00:00:02 runsvdir: aec index 1091836049 line 1125 gain 1.000000 
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390531][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390531][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390532][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390532][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:02 runsvdir: 3659b000 367b9000 36842000 0 0 0 368c5000 36ae3000 36b6c000 0 0 0 
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390532][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390532][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4384 power on vc 0 0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390533][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390534][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390535][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4386 power on vc end 0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390542][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390542][0298][0x7f7568a200][SYS_CORE][VI_KEY] mipi_rx_9411.c: mipi_rx_power_up_9411 : 64 enable the mipi cfg and pcs clock, and set pcs to clk=100000000hz^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390542][0298][0x7f7568a200][SYS_CORE][VI_KEY] mipi_rx.c: one_mipi_init : 1204 mipi 4 init done^[[0m
Jan 25 00:00:02 runsvdir: aec index 58856738 line 7042 gain 1.000000 
Jan 25 00:00:02 runsvdir:  imx681_stream_on
Jan 25 00:00:02 runsvdir:  imx681_trigger_on
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390542][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390542][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] stream.c: link_filter_external : 407 check_linking_stream error:-2146402228^[[0m
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390542][0298][0x7f7568a200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390543][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=1^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390543][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=1^[[0m
Jan 25 00:00:02 runsvdir: aec index 1073743872 line 7042 gain 1.000000 
Jan 25 00:00:02 runsvdir:  imx681_stream_on
Jan 25 00:00:02 runsvdir:  imx681_trigger_on
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390543][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390547][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390549][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390549][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390549][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 2^[[0m
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390551][0482][0x7f7dd2e010][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390551][0298][0x7f760b5200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390551][0298][0x7f760b5200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390551][0298][0x7f760b5200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124390551][0298][0x7f771d0200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390551][0298][0x7f771d0200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390551][0298][0x7f75749200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390551][0298][0x7f771d0200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390551][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390551][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390552][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390552][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390552][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:02 runsvdir: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390552][0298][0x7f771d0200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:02 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390552][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:02 runsvdir: 35f41000 3615f000 361e8000 0 0 0 3626b000 36489000 36512000 0 0 0 
Jan 25 00:00:02 runsvdir: aec index ********** line 1125 gain 1.000000 
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390552][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390552][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:02 runsvdir: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:02 runsvdir: ^[[31;22m[124390552][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:02 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:02 runsvdir: 3659b000 367b9000 36842000 0 0 0 368c5000 36ae3000 36b6c000 0 0 0 
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390553][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390554][0298][0x7f75870200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_enable : 456 subvision already enable!^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390554][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390556][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390556][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:02 runsvdir: ^[[33;22m[124390556][0298][0x7f75870200][VO_CORE][WARN] display_top.c: display_offset_vo_src : 1520 hardware_diff_us:6635.116699 first_skewing_us:6635.116699!
Jan 25 00:00:02 runsvdir: ^[[0m
Jan 25 00:00:02 runsvdir: ^[[35;22m[124390559][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390559][0298][0x7f76464200][SYS_CORE][VI_KEY] vif_mid.c: vexfatprogs version : 1.2.5
Jan 25 00:00:03 runsvdir: /dev/mmcblk0p22: clean. directories 1, files 0
Jan 25 00:00:03 runsvdir: [storage_service] DEBUG storage.c get_usb_storage_available:703: :0
Jan 25 00:00:03 runsvdir: [storage_service] DEBUG storage.c get_total_size:384: service: total_size:2145910784
Jan 25 00:00:03 runsvdir: [storage_service] DEBUG storage.c get_available_size:398: service: available_size:2145746944
Jan 25 00:00:03 runsvdir: [audio service]:[audio]:handleClientRequest cmd[45]
Jan 25 00:00:03 runsvdir: [audio service]:[audio binder service]:GET_SDK_MUTE_STATUS sdk_mute[1]
Jan 25 00:00:03 runsvdir: [audio service]:[audio]:handleClientRequest cmd[46]
Jan 25 00:00:03 runsvdir: [audio service]:[audio binder service]:SET_SDK_MUTE_STATUS sdk_mute[0]
Jan 25 00:00:03 runsvdir: [audio service]:[audio]:handleClientRequest cmd[45]
Jan 25 00:00:03 runsvdir: [audio service]:[audio binder service]:GET_SDK_MUTE_STATUS sdk_mute[0]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.306] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.509_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.307] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.336] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.509_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.336] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.337] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.509_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.600] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.509_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.600] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.601] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 0016
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.618] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.509_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.619] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.619] [function/upgrade_function.c] [read_dsp_version-1046] : lock dsp
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.621] [function/upgrade_function.c] [read_dsp_version-1052] : dsp version : 15.A.00.069_20241211
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.621] [common/common.c] [update_property_file-1314] : key offset [0], value offset [20] eof offset [41]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.621] [common/common.c] [update_property_file-1338] : file [/persist/device.prop] key [sys.bsp.dsp_version] value is the same as setting [15.A.00.069_20241211]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.644] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.509_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.645] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.645] [function/upgrade_function.c] [read_sdk_app_version-636] : version file : /usrdata/pilot/pilot_version.txt
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.645] [common/common.c] [wait_property_ready-1174] : waiting for property [ro.bsp.app_prepare_done] value [true], force waiting [false], timeout 40 s
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.645] [common/common.c] [wait_property_ready-1183] : get value
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.645] [common/common.c] [wait_property_ready-1218] : property [ro.bsp.app_prepare_done], aiming value [true], read value [true]
Jan 25 00:00:05 rcS: not found hdcp node
Jan 25 00:00:05 runsvdir: set S_VDD_0V8 and S_VDD_DOV8 to 775mV
Jan 25 00:00:06 runsvdir: 681_vin_init)INIT-S18.1: AR_MPI_ISP_SetPubAttr^[[0m
Jan 25 00:00:06 runsvdir: cmos_set_wdr_mode: linear mode
Jan 25 00:00:06 runsvdir: cmos_set_image_mode:4
Jan 25 00:00:06 runsvdir: ^[[32;22mD/[25 00:00:02.452670] [Scam]: (1923 xr_imx681_vin_init)INIT-S19.1: AR_MPI_VI_GetPipeExtAttr^[[0m
Jan 25 00:00:06 runsvdir: tuning file not exit, please check ...^[[32;22mD/[25 00:00:02.452705] [Scam]: (1945 xr_imx681_vin_init)INIT-S20.1: AR_MPI_VI_SetPipeExtAttr^[[0m
Jan 25 00:00:06 runsvdir: ^[[32;22mD/[25 00:00:02.452714] [Scam]: (1954 xr_imx681_vin_init)INIT-S21.1: AR_MPI_ISP_Init^[[0m
Jan 25 00:00:06 runsvdir: tuning file not exit, please check ...^[[32;22mD/[25 00:00:02.452733] [Scam]: (1961 xr_imx681_vin_init)INIT-S22.1: AR_MPI_ISP_Run^[[0m
Jan 25 00:00:06 runsvdir: ^[[32;22mD/[25 00:00:02.458628] [Scam]: (1969 xr_imx681_vin_init)INIT-S23.1: trigger AEC AWB^[[0m
Jan 25 00:00:06 runsvdir: ^[[36;22mI/[25 00:00:02.458659] [Scam]: (9372 USER_Camera_Init)xr_imx681_vin_init stream[9] sensor[4] success.^[[0m
Jan 25 00:00:06 runsvdir: ^[[36;22mI/[25 00:00:02.458666] [Scam]: (4911 streamSwitch)USER_Camera_Init success! stream type = 9.^[[0m
Jan 25 00:00:06 runsvdir: ^[[36;22mI/[25 00:00:02.458673] [Scam]: (4666 updateStreamRunningStatus)update stream running status to 3^[[0m
Jan 25 00:00:06 runsvdir: ^[[36;22mI/[25 00:00:02.458678] [Scam]: (4685 updateStreamTypeStatus)update stream type status to 9^[[0m
Jan 25 00:00:06 runsvdir: ^[[32;22mD/[25 00:00:02.458727] [Scam]: (96 binder_message_dequeue)binder_message_dequeue,  q->count:0 ^[[0m
Jan 25 00:00:06 runsvdir: ^[[33;22mW/[25 00:00:02.458734] [Scam]: (100 binder_message_dequeue)binder_message_dequeue is empty, waitting ...^[[0m
Jan 25 00:00:06 runsvdir: DisplayService I : get high pricision fps = 90.003357
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 2.953 ]
Jan 25 00:00:06 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp init vi done> -- [ 2.968 ]
Jan 25 00:00:06 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:00:06 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:00:06 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:00:06 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp init isp > -- [ 2.992 ]
Jan 25 00:00:06 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:00:06 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:00:06 runsvdir: DisplayService I : set duty 0
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 2.994 ]
Jan 25 00:00:06 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:00:06 runsvdir: DisplayService I : set duty 16
Jan 25 00:00:06 runsvdir: DisplayService I : Set original LUT...
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp init isp done> -- [ 2.999 ]
Jan 25 00:00:06 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:00:06 runsvdir: DisplayService W : can't find node 0x7f70001dd0, ignore this release
Jan 25 00:00:06 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:00:06 runsvdir: DisplayService I : set duty 16
Jan 25 00:00:06 runsvdir: DisplayService W : can't find node 0x7f700020a0, ignore this release
Jan 25 00:00:06 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:06 runsvdir: DisplayService I : current vo's refresh rate 90Hz, return 3
Jan 25 00:00:06 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:06 runsvdir: DisplayService I : get dp info 1920x1080@90
Jan 25 00:00:06 runsvdir: DisplayService I : Get frame stats: Successes 58.67, Failures 0.33
Jan 25 00:00:06 runsvdir: DisplayService I : dpvsync : 0.00
Jan 25 00:00:06 runsvdir: DisplayService I : DPISP LUT mode matches, expected mode: 0, current mode: 0 (pipe: 2)
Jan 25 00:00:06 runsvdir: DisplayService I : set dpisp filter mode:1
Jan 25 00:00:06 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:00:06 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:06 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:06 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:00:06 runsvdir: DisplayService I : set duty 0
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 5.482 ]
Jan 25 00:00:06 runsvdir: DisplayService I : Screen successfully closed.
Jan 25 00:00:06 runsvdir: DisplayService I : switch edid to 5 without audio
Jan 25 00:00:06 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:00:06 runsvdir: DisplayService I : get edid 0, dp_audio 0
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp get a video change interrupt> -- [ 5.494 ]
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp DP status changed start> -- [ 5.694 ]
Jan 25 00:00:06 runsvdir: DisplayService I : dp status changed, 1920x1080@90 -> 0x0@0
Jan 25 00:00:06 runsvdir: DisplayService I : err: fps:0, H:0, V:0
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp DP status changed end> -- [ 5.694 ]
Jan 25 00:00:06 runsvdir: DisplayService I : notify dp info update
Jan 25 00:00:06 runsvdir: DisplayService I : get a dp node, state 2
Jan 25 00:00:06 runsvdir: DisplayService I : broadcast dp event : 2
Jan 25 00:00:06 runsvdir: DisplayService I : release this event
Jan 25 00:00:06 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp send edid end> -- [ 6.983 ]
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 7.10 ]
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 7.10 ]
Jan 25 00:00:06 runsvdir: DisplayService I : free vb count = 8, ^[[0;31mDisplayService E: __handleClientRequest[1548] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1548] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1548] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: if_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:07 runsvdir: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:07 runsvdir: ^[[31;22m[124390616][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:07 runsvdir: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:07 runsvdir: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:00:07 runsvdir: ^[[31;22m[124390619][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:07 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:07 runsvdir: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:07 runsvdir: ^[[31;22m[124390798][0298][0x7f76464200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [dp_scaler_lut_direct_mode_1] failed!^[[0m
Jan 25 00:00:07 runsvdir: yuv420 out, user have set the lut, use user lut
Jan 25 00:00:07 runsvdir: ^[[35;22m[124390953][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124390953][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124390954][0298][0x7f7578b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[124391001][0298][0x7f75870200][VO_CORE][ERROR] display_top.c: display_offset_vo_src : 1510 src_de_over_time_us:-12544.269531 is more than mod_de_time_us:11110.129883(one frame), skewing_err_cnt:1^[[0m
Jan 25 00:00:07 runsvdir: reference_clock:        300.000000Mhz
Jan 25 00:00:07 runsvdir: src_vsync_sel:          SRC_DP_ISP
Jan 25 00:00:07 runsvdir: de_vsync_sel:           DE1
Jan 25 00:00:07 runsvdir: src_vsync_polarity:     0
Jan 25 00:00:07 runsvdir: de_vsync_polarity:      0
Jan 25 00:00:07 runsvdir: src_frame               16666.042969 us, 60.002245 fps
Jan 25 00:00:07 runsvdir: de_frame                11110.129883 us, 90.007949 fps
Jan 25 00:00:07 runsvdir: vsync_delay_time(limit) 9675.993164 us
Jan 25 00:00:07 runsvdir: vsync_delay_time(over)  -12544.269531 us
Jan 25 00:00:07 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.160176][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.160251][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.160402][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.160415][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391013][0298][0x7f76dad200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391013][0298][0x7f7576a200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391013][0298][0x7f755a7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391013][0298][0x7f755a7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391014][0298][0x7f760b5200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391014][0298][0x7f760b5200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391014][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391014][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391014][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 runsvdir: 35f41000 3637b000 3648b000 0 0 0 36595000 369cf000 36adf000 0 0 0 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391014][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 runsvdir: aec index 22022179 line 1125 gain 1.000000 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391014][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391014][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391014][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_ainused vb count = 0
Jan 25 00:00:07 runsvdir: DisplayService W : can't find node 0x7f70001dd0, ignore this release
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 7.17 ]
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f77e0e108
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 7.18 ]
Jan 25 00:00:07 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp get a video change interrupt> -- [ 7.409 ]
Jan 25 00:00:07 runsvdir: DisplayService I : pixel clock: 297000000, fps = 60
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService I : Get frame stats: Successes 32.67, Failures 1.00
Jan 25 00:00:07 runsvdir: DisplayService I : dpvsync : 0.00
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp DP status changed start> -- [ 7.609 ]
Jan 25 00:00:07 runsvdir: DisplayService I : dp status changed, 0x0@0 -> 3840x1080@60
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp DP status changed end> -- [ 7.609 ]
Jan 25 00:00:07 runsvdir: DisplayService I : notify dp info update
Jan 25 00:00:07 runsvdir: DisplayService I : get a dp node, state 1
Jan 25 00:00:07 runsvdir: DisplayService I : should init mppvi
Jan 25 00:00:07 runsvdir: DisplayService I : broadcast dp event : 1
Jan 25 00:00:07 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:07 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:07 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f77e0e108
Jan 25 00:00:07 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:07 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:00:07 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:07 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:07 runsvdir: DisplayService I : release this event
Jan 25 00:00:07 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:07 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:00:07 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:07 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:07 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:00:07 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:07 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:00:07 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:07 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:00:07 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:07 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:07 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 60.000000
Jan 25 00:00:07 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:00:07 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:07 runsvdir: read vb config data index:4 success.
Jan 25 00:00:07 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:00:07 runsvdir: read vb config data index:4 success.
Jan 25 00:00:07 runsvdir: get vb pool id <4>!
Jan 25 00:00:07 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:00:07 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:00:07 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp init vi done> -- [ 7.634 ]
Jan 25 00:00:07 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:00:07 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 7.634 ]
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 7.634 ]
Jan 25 00:00:07 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 7.637 ]
Jan 25 00:00:07 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f77e0e108
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 7.637 ]
Jan 25 00:00:07 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:00:07 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:00:07 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:00:07 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:00:07 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:00:07 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:00:07 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:07 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:07 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f77e0e108
Jan 25 00:00:07 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:07 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:00:07 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:07 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:07 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:00:07 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:07 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:07 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:00:07 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:07 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:00:07 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:07 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:00:07 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:07 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:07 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 60.000000
Jan 25 00:00:07 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:00:07 runsvdir: read vb config data index:4 success.
Jan 25 00:00:07 runsvdir: DisplaySerpi.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391015][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391016][0298][0x7f7576a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.209671][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.209705][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.211899][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.211926][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391018][0298][0x7f771d0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391018][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0298][0x7f7568a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0298][0x7f75586200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 runsvdir: 35f41000 3637b000 3648b000 0 0 0 36595000 369cf000 36adf000 0 0 0 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 runsvdir: aec index 22022179 line 1125 gain 1.000000 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0298][0x7f75586200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0298][0x7f75586200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 runsvdir: 3659b000 369d5000 36ae5000 0 0 0 36bef000 37029000 37139000 0 0 0 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391021][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391023][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391035][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391035][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[124391038][0482][0x7f4e72f1a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391038][0298][0x7f760f7200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[124391038][0298][0x7f760f7200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124391038][0298][0x7f755a7200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391039][0298][0x7f7568a200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391039][0298][0x7f760f7200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391039][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391039][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391039][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:10 runsvdir: ^[[35;22m[124391039][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on[audio service]:snd_numid_handle :7 48000
Jan 25 00:00:10 runsvdir: set S_VDD_0V8 and S_VDD_DOV8 to 762.5mV
Jan 25 00:00:11 runsvdir: vice I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:00:11 runsvdir: read vb config data index:4 success.
Jan 25 00:00:11 runsvdir: get vb pool id <4>!
Jan 25 00:00:11 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 7.669 ]
Jan 25 00:00:11 runsvdir: DisplayService I : get high pricision fps = 60.002247
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 7.829 ]
Jan 25 00:00:11 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp init vi done> -- [ 7.833 ]
Jan 25 00:00:11 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:00:11 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:00:11 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:00:11 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp init isp > -- [ 7.867 ]
Jan 25 00:00:11 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:00:11 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:00:11 runsvdir: DisplayService I : set duty 0
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 7.869 ]
Jan 25 00:00:11 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:00:11 runsvdir: DisplayService I : set duty 16
Jan 25 00:00:11 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp init isp done> -- [ 7.874 ]
Jan 25 00:00:11 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:00:11 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:00:11 runsvdir: DisplayService I : set duty 16
Jan 25 00:00:11 runsvdir: DisplayService W : can't find node 0x7f700020a0, ignore this release
Jan 25 00:00:11 runsvdir: DisplayService W : can't find node 0x7f70002370, ignore this release
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : enable dpisp vsync
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : Get frame stats: Successes 50.67, Failures 0.00
Jan 25 00:00:11 runsvdir: DisplayService I : dpvsync : 7.00
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:00:11 runsvdir: DisplayService I : set duty 0
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 11.786 ]
Jan 25 00:00:11 runsvdir: DisplayService I : Screen successfully closed.
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:00:11 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 11.796 ]
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 11.796 ]
Jan 25 00:00:11 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:00:11 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 11.819 ]
Jan 25 00:00:11 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f77e0e108
Jan 25 00:00:11 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:00:11 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 11.821 ]
Jan 25 00:00:11 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:00:11 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:00:11 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:00:11 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:11 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:11 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f77e0e108
Jan 25 00:00:11 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:11 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:00:11 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:11 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:11 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:00:11 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:11 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:11 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:00:11 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:11 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:00:11 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:11 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:00:11 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:11 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:11 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 60.000000
Jan 25 00:00:11 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:00:11 runsvdir: read vb config data index:4 success.
Jan 25 00:00:11 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:00:11 runsvdir: read vb config data index:4 success.
Jan 25 00:00:11 runsvdir: get vb pool id <4>!
Jan 25 00:00:11 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 11.832 ]
Jan 25 00:00:11 runsvdir: DisplayService I : get high pricision fps = 60.002258
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 11.993 ]
Jan 25 00:00:11 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:00:11 runsvdir: Dis 1^[[0m
Jan 25 00:00:11 runsvdir: 35f41000 3637b000 3648b000 0 0 0 36595000 369cf000 36adf000 0 0 0 
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391039][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391039][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:11 runsvdir: aec index ********** line 1125 gain 1.000000 
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391039][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391039][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:11 runsvdir: 3659b000 369d5000 36ae5000 0 0 0 36bef000 37029000 37139000 0 0 0 
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391041][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391043][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:11 runsvdir: ^[[33;22m[124391432][0298][0x7f760b5200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:11 runsvdir: ^[[33;22m[124391432][0298][0x7f7576a200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391432][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391433][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391434][0298][0x7f76dad200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:11 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:11 runsvdir: ^[[31;22m[25 00:00:11.373451][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:11 runsvdir: ^[[31;22m[25 00:00:11.373490][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:11 runsvdir: ^[[31;22m[25 00:00:11.373610][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:11 runsvdir: ^[[31;22m[25 00:00:11.373621][0447][0x7f3effd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391434][0298][0x7f760b5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391435][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391435][0298][0x7f75749200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391435][0298][0x7f755c8200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391435][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:11 runsvdir: 35f41000 3637b000 3648b000 0 0 0 36595000 369cf000 36adf000 0 0 0 
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391435][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391435][0298][0x7f76094200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:11 runsvdir: aec index 22022179 line 1125 gain 1.000000 
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391435][0298][0x7f755c8200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391435][0298][0x7f755c8200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:11 runsvdir: 3659b000 369d5000 36ae5000 0 0 0 36bef000 37029000 37139000 0 0 0 
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391438][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391440][0298][0x7f76464200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391451][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391453][0298][0x7f760f7200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:52 runsvdir: ^[[31;22m[124ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:52 runsvdir: [audio service]:try_again
Jan 25 00:01:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:16 runsvdir: [audio service]:try_again
Jan 25 00:01:20 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:20 runsvdir: [audio service]:try_again
Jan 25 00:01:39 runsvdir: playService I : <timestamp init vi done> -- [ 12.8 ]
Jan 25 00:01:39 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:01:39 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:01:39 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:01:39 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:01:39 runsvdir: DisplayService I : <timestamp init isp > -- [ 12.31 ]
Jan 25 00:01:39 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:01:39 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:01:39 runsvdir: DisplayService I : set duty 0
Jan 25 00:01:39 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 12.34 ]
Jan 25 00:01:39 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:01:39 runsvdir: DisplayService I : set duty 16
Jan 25 00:01:39 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:01:39 runsvdir: DisplayService I : <timestamp init isp done> -- [ 12.40 ]
Jan 25 00:01:39 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:01:39 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:01:39 runsvdir: DisplayService I : set duty 16
Jan 25 00:01:39 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:01:39 runsvdir: DisplayService W : can't find node 0x7f70002be0, ignore this release
Jan 25 00:01:39 runsvdir: DisplayService W : can't find node 0x7f70002eb0, ignore this release
Jan 25 00:01:39 runsvdir: DisplayService W : can't find node 0x7f70003180, ignore this release
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 54.67, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 58.67
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 59.67
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:44 runsvdir: DisplayServicALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:44 runsvdir: [audio service]:try_again
Jan 25 00:01:44 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:44 runsvdir: [audio service]:try_again
Jan 25 00:02:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:04 runsvdir: [audio service]:try_again
Jan 25 00:03:02 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:02 runsvdir: [audio service]:try_again
Jan 25 00:03:03 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:03 runsvdir: [audio service]:try_again
Jan 25 00:03:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:04 runsvdir: [audio service]:try_again
Jan 25 00:03:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:12 runsvdir: [audio service]:try_again
Jan 25 00:03:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:30 runsvdir: [audio service]:try_again
Jan 25 00:03:31 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:31 runsvdir: [audio service]:try_again
Jan 25 00:03:39 runsvdir: e I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:44 runsvdir: DisplayService I : dpvsync : ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:44 runsvdir: [audio service]:try_again
Jan 25 00:04:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:09 runsvdir: [audio service]:try_again
Jan 25 00:04:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:24 runsvdir: [audio service]:try_again
Jan 25 00:04:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:42 runsvdir: [audio service]:try_again
Jan 25 00:05:03 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:03 runsvdir: [audio service]:try_again
Jan 25 00:05:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:22 runsvdir: [audio service]:try_again
Jan 25 00:05:43 runsvdir: 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:05:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:05:51 runsvdir: DisplaySerALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:51 runsvdir: [audio service]:try_again
Jan 25 00:05:52 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:52 runsvdir: [audio service]:try_again
Jan 25 00:06:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:05 runsvdir: [audio service]:try_again
Jan 25 00:06:08 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:08 runsvdir: [audio service]:try_again
Jan 25 00:06:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:18 runsvdir: [audio service]:try_again
Jan 25 00:07:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:32 runsvdir: [audio service]:try_again
Jan 25 00:07:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:34 runsvdir: [audio service]:try_again
Jan 25 00:07:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:34 runsvdir: [audio service]:try_again
Jan 25 00:07:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:37 runsvdir: [audio service]:try_again
Jan 25 00:07:43 runsvdir: vice I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:07:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:07:44 runsvdir: DisplayService I : Get fraALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:44 runsvdir: [audio service]:try_again
Jan 25 00:07:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:53 runsvdir: [audio service]:try_again
Jan 25 00:07:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:57 runsvdir: [audio service]:try_again
Jan 25 00:08:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:37 runsvdir: [audio service]:try_again
Jan 25 00:08:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:53 runsvdir: [audio service]:try_again
Jan 25 00:09:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:06 runsvdir: [audio service]:try_again
Jan 25 00:09:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:27 runsvdir: [audio service]:try_again
Jan 25 00:09:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:30 runsvdir: [audio service]:try_again
Jan 25 00:09:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:39 runsvdir: [audio service]:try_again
Jan 25 00:09:43 runsvdir: me stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:09:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:09:46 runsvdir: DisplayService I : Get frame stats: SuccesALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:46 runsvdir: [audio service]:try_again
Jan 25 00:10:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:26 runsvdir: [audio service]:try_again
Jan 25 00:10:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:38 runsvdir: [audio service]:try_again
Jan 25 00:10:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:38 runsvdir: [audio service]:try_again
Jan 25 00:10:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:57 runsvdir: [audio service]:try_again
Jan 25 00:11:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:11:40 runsvdir: [audio service]:try_again
Jan 25 00:11:43 runsvdir: ses 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:11:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:11:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:12:29 runsvdir: DisplayService I : Get frame stats: Successes 60.00, FailuALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:12:29 runsvdir: [audio service]:try_again
Jan 25 00:12:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:12:34 runsvdir: [audio service]:try_again
Jan 25 00:13:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:13:06 runsvdir: [audio service]:try_again
Jan 25 00:13:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:13:26 runsvdir: [audio service]:try_again
Jan 25 00:13:31 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:13:31 runsvdir: [audio service]:try_again
Jan 25 00:13:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:13:33 runsvdir: [audio service]:try_again
Jan 25 00:13:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:13:34 runsvdir: [audio service]:try_again
Jan 25 00:13:43 runsvdir: res 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:43 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:13:43 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:13:50 runsvdir: DisplayALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:13:50 runsvdir: [audio service]:try_again
Jan 25 00:13:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:13:50 runsvdir: [audio service]:try_again
Jan 25 00:14:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:14:23 runsvdir: [audio service]:try_again
Jan 25 00:14:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:14:27 runsvdir: [audio service]:try_again
Jan 25 00:14:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:14:35 runsvdir: [audio service]:try_again
Jan 25 00:14:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:14:42 runsvdir: [audio service]:try_again
